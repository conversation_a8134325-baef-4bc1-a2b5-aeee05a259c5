import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import time
from typing import Dict, Tuple, List

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from lbm_core import LBMCore
from fluid_path_planner import FluidSimulator
from visualization import FluidVisualization
import warnings
warnings.filterwarnings('ignore')

# 设置全局字体为Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['axes.titleweight'] = 'bold'

class LBMAlgorithmComparison:
    """LBM算法对比类"""

    def __init__(self):
        """初始化对比系统"""
        # 明确的算法参数
        self.algorithm_params = {
            'traditional_lbm': {
                'tau': 0.8,                     # 松弛时间
                'inlet_velocity': 0.5,          # 入口速度
                'max_iterations': 10000,         # 最大迭代次数
                'boundary_type': 'zou_he',      # 边界条件类型
                'collision_model': 'bgk',       # 碰撞模型
                'flow_direction_mode': 'point_to_point',  # 流动方向模式（perpendicular）：垂直于入口边界线
                'convergence_threshold': 1e-10,  # 收敛阈值
                'print_interval': 500,          # 打印间隔
                'use_multi_scale_bc': False,    # 不使用多尺度边界条件
                'use_adaptive_tau': False,      # 不使用自适应时间步长
                'enable_parallel': True         # 启用并行计算
            },
            'enhanced_lbm': {
                'tau': 0.8,                     # 松弛时间（更稳定）
                'inlet_velocity': 0.5,         # 入口速度（更低）
                'max_iterations': 10000,         # 最大迭代次数
                'boundary_type': 'zou_he',      # 边界条件类型
                'collision_model': 'bgk',       # 碰撞模型
                'flow_direction_mode': 'point_to_point',  # 流动方向模式（point_to_point）：起点到终点
                'convergence_threshold': 1e-10,  # 更严格的收敛阈值
                'print_interval': 500,          # 打印间隔
                'use_multi_scale_bc': True,     # 使用多尺度边界条件
                'use_adaptive_tau': True,       # 使用自适应时间步长
                'enable_parallel': True         # 启用并行计算
            }
        }

        # 测试场景参数
        self.test_scenario = {
            'grid_size': (40, 50),             # 网格尺寸
            'obstacle_density': 0.15,          # 障碍物密度
            'random_seed': 42,                 # 随机种子（确保可复现）
            'small_obstacle_ratio': 0.6,       # 小障碍物比例
            'large_obstacle_count': 3          # 大障碍物数量
        }

        # 输出参数
        self.output_params = {
            'save_results': True,
            'output_dir': 'lbm_algorithm_comparison_results',
            'image_dpi': 300,
            'image_format': 'png'
        }

        # 创建输出目录
        os.makedirs(self.output_params['output_dir'], exist_ok=True)

    def create_test_grid(self) -> np.ndarray:
        """
        创建测试网格

        返回:
        grid_map: 测试网格地图
        """
        h, w = self.test_scenario['grid_size']
        np.random.seed(self.test_scenario['random_seed'])

        # 初始化空网格
        grid_map = np.zeros((h, w), dtype=int)

        # 添加小障碍物（随机分布）
        small_obstacle_count = int(h * w * self.test_scenario['obstacle_density'] *
                                  self.test_scenario['small_obstacle_ratio'])

        for _ in range(small_obstacle_count):
            x = np.random.randint(5, h-5)
            y = np.random.randint(5, w-5)
            # 创建1-3格点的小障碍物
            size = np.random.randint(1, 4)
            grid_map[x:x+size, y:y+size] = 1

        # 添加大障碍物（规则形状）
        for i in range(self.test_scenario['large_obstacle_count']):
            if i == 0:  # 矩形障碍物
                grid_map[10:18, 15:25] = 1
            elif i == 1:  # L形障碍物
                grid_map[25:35, 30:33] = 1  # 垂直部分
                grid_map[32:35, 25:35] = 1  # 水平部分
            elif i == 2:  # 圆形障碍物
                center_x, center_y = 15, 35
                radius = 4
                for x in range(h):
                    for y in range(w):
                        if (x - center_x)**2 + (y - center_y)**2 <= radius**2:
                            grid_map[x, y] = 1

        return grid_map

    def load_sea_ice_map(self) -> Dict:
        """
        加载真实的海冰地图，包含标签交换功能

        返回:
        map_data: 包含原始地图和扩展地图的字典
        """
        from src.utils import DataProcessor, FluidAnalyzer

        # 尝试加载sea_ice_419.xlsx文件
        map_data = DataProcessor.load_sea_ice_map("sea_ice_419.xlsx")

        return map_data


    def run_traditional_lbm(self, grid_map: np.ndarray, map_data: Dict) -> Dict:
        """
        运行传统LBM算法

        参数:
        grid_map: 网格地图
        map_data: 地图数据

        返回:
        results: 算法运行结果
        """
        print("🔄 运行传统LBM算法...")

        params = self.algorithm_params['traditional_lbm']

        # 初始化模拟器，传递起点终点和地图数据
        simulator = FluidSimulator(grid_map, map_data['start_point'], map_data['end_point'], map_data)
        simulator.prepare_grid_with_inlet_outlet()

        print(f"  📋 传统LBM参数详情:")
        print(f"    松弛时间 (tau): {params['tau']}")
        print(f"    入口速度: {params['inlet_velocity']}")
        print(f"    流动方向模式: {params['flow_direction_mode']}")
        print(f"    边界条件类型: {params['boundary_type']}")
        print(f"    碰撞模型: {params['collision_model']}")
        print(f"    最大迭代次数: {params['max_iterations']}")
        print(f"    收敛阈值: {params['convergence_threshold']}")

        # 使用FluidSimulator的run_fluid_simulation方法
        start_time = time.time()

        try:
            simulator.run_fluid_simulation(
                max_iter=params['max_iterations'],
                tau=params['tau'],
                inlet_velocity=params['inlet_velocity'],
                convergence_threshold=params['convergence_threshold'],
                collision_model=params['collision_model'],
                boundary_condition=params['boundary_type'],
                flow_direction_mode=params['flow_direction_mode']
            )
        except Exception as e:
            print(f"  ❌ 传统LBM模拟失败: {e}")
            # 回退到简化的手动循环
            print("  🔄 回退到手动模拟循环...")

            # 记录性能指标
            convergence_history = []
            velocity_oscillations = []

            # 初始化收敛监控变量
            prev_u = None
            prev_v = None
            prev_rho = None

            # 运行LBM模拟
            for step in range(params['max_iterations']):
                # 保存前一步的状态用于残差计算
                if step > 0:
                    prev_u = simulator.lbm_core.u.copy()
                    prev_v = simulator.lbm_core.v.copy()
                    prev_rho = simulator.lbm_core.rho.copy()

                # 计算宏观量
                simulator.lbm_core.compute_macroscopic()

                # 应用边界条件，传递统一的入口出口位置和边界线
                simulator.lbm_core.apply_boundary_conditions(
                    simulator.grid_map,
                    params['inlet_velocity'],
                    params['boundary_type'],
                    simulator.start_point,  # 入口位置
                    simulator.end_point,    # 出口位置
                    simulator.inlet_boundary_line,   # 入口边界线
                    simulator.outlet_boundary_line,   # 出口边界线
                    params['flow_direction_mode']     # 流动方向模式
                )

            # 标准反弹边界条件
            obstacle_mask = (simulator.grid_map == 1)
            simulator.lbm_core.bounce_back(obstacle_mask)

            # BGK碰撞（固定tau）
            simulator.lbm_core.collision_bgk(params['tau'])

            # 流动
            simulator.lbm_core.streaming()

            # 记录收敛指标
            if step % 10 == 0:
                max_velocity = np.max(np.sqrt(simulator.lbm_core.u**2 + simulator.lbm_core.v**2))
                convergence_history.append(max_velocity)

                if len(convergence_history) > 1:
                    oscillation = abs(convergence_history[-1] - convergence_history[-2])
                    velocity_oscillations.append(oscillation)

            # 每500步打印详细收敛状态
            if step % 500 == 0:
                max_velocity = np.max(np.sqrt(simulator.lbm_core.u**2 + simulator.lbm_core.v**2))
                avg_density = np.mean(simulator.lbm_core.rho)

                # 计算残差
                if prev_u is not None:
                    velocity_residual = np.sqrt(np.mean((simulator.lbm_core.u - prev_u)**2 +
                                                       (simulator.lbm_core.v - prev_v)**2))
                    density_residual = np.sqrt(np.mean((simulator.lbm_core.rho - prev_rho)**2))
                    print(f"    步骤 {step:4d}: 最大速度={max_velocity:.6f}, 平均密度={avg_density:.6f}, "
                          f"速度残差={velocity_residual:.2e}, 密度残差={density_residual:.2e}")
                else:
                    print(f"    步骤 {step:4d}: 最大速度={max_velocity:.6f}, 平均密度={avg_density:.6f}, "
                          f"速度残差=N/A, 密度残差=N/A")

        simulation_time = time.time() - start_time

        # 计算最终性能指标
        final_velocity_field = np.sqrt(simulator.lbm_core.u**2 + simulator.lbm_core.v**2)
        velocity_smoothness = 1 / (1 + np.std(final_velocity_field))

        # 如果使用了FluidSimulator的run_fluid_simulation，则没有手动的convergence_history
        if 'convergence_history' not in locals():
            convergence_history = []
            velocity_oscillations = []
            avg_oscillation = 0
        else:
            avg_oscillation = np.mean(velocity_oscillations) if velocity_oscillations else 0

        results = {
            'simulator': simulator,
            'simulation_time': simulation_time,
            'convergence_history': convergence_history,
            'velocity_oscillations': velocity_oscillations,
            'final_max_velocity': np.max(final_velocity_field),
            'velocity_smoothness': velocity_smoothness,
            'avg_oscillation': avg_oscillation,
            'algorithm_type': 'Traditional LBM',
            'flow_direction_mode': params['flow_direction_mode']
        }

        print(f"  ✅ 传统LBM完成，耗时: {simulation_time:.3f}秒")
        print(f"    流动方向模式: {params['flow_direction_mode']}")
        print(f"    最大速度: {np.max(final_velocity_field):.6f}")
        print(f"    速度场平滑度: {velocity_smoothness:.6f}")
        return results

    def run_enhanced_lbm(self, grid_map: np.ndarray, map_data: Dict) -> Dict:
        """
        运行增强LBM算法

        参数:
        grid_map: 网格地图
        map_data: 地图数据

        返回:
        results: 算法运行结果
        """
        print("🚀 运行增强LBM算法...")

        params = self.algorithm_params['enhanced_lbm']

        # 初始化模拟器，传递起点终点和地图数据
        simulator = FluidSimulator(grid_map, map_data['start_point'], map_data['end_point'], map_data)
        simulator.prepare_grid_with_inlet_outlet()

        print(f"  📋 增强LBM参数详情:")
        print(f"    松弛时间 (tau): {params['tau']}")
        print(f"    入口速度: {params['inlet_velocity']}")
        print(f"    流动方向模式: {params['flow_direction_mode']}")
        print(f"    边界条件类型: {params['boundary_type']}")
        print(f"    碰撞模型: {params['collision_model']}")
        print(f"    最大迭代次数: {params['max_iterations']}")
        print(f"    收敛阈值: {params['convergence_threshold']}")
        print(f"    多尺度边界条件: {params['use_multi_scale_bc']}")
        print(f"    自适应时间步长: {params['use_adaptive_tau']}")

        # 使用FluidSimulator的run_fluid_simulation方法
        start_time = time.time()

        try:
            simulator.run_fluid_simulation(
                max_iter=params['max_iterations'],
                tau=params['tau'],
                inlet_velocity=params['inlet_velocity'],
                convergence_threshold=params['convergence_threshold'],
                collision_model=params['collision_model'],
                boundary_condition=params['boundary_type'],
                flow_direction_mode=params['flow_direction_mode']
            )
        except Exception as e:
            print(f"  ❌ 增强LBM模拟失败: {e}")
            # 回退到简化的手动循环
            print("  🔄 回退到手动模拟循环...")

            # 记录性能指标
            convergence_history = []
            velocity_oscillations = []
            tau_history = []

            # 初始化收敛监控变量
            prev_u = None
            prev_v = None
            prev_rho = None

            # 运行增强LBM模拟
            for step in range(params['max_iterations']):
                # 保存前一步的状态用于残差计算
                if step > 0:
                    prev_u = simulator.lbm_core.u.copy()
                    prev_v = simulator.lbm_core.v.copy()
                    prev_rho = simulator.lbm_core.rho.copy()

                # 计算宏观量
                simulator.lbm_core.compute_macroscopic()

                # 应用边界条件，传递统一的入口出口位置和边界线
                simulator.lbm_core.apply_boundary_conditions(
                    simulator.grid_map,
                    params['inlet_velocity'],
                    params['boundary_type'],
                    simulator.start_point,  # 入口位置
                    simulator.end_point,    # 出口位置
                    simulator.inlet_boundary_line,   # 入口边界线
                    simulator.outlet_boundary_line,   # 出口边界线
                    params['flow_direction_mode']     # 流动方向模式
                )

            # 增强的多尺度边界处理
            obstacle_mask = (simulator.grid_map == 1)
            if params['use_multi_scale_bc']:
                try:
                    simulator.lbm_core.multi_scale_boundary_treatment(obstacle_mask)
                except AttributeError:
                    simulator.lbm_core.bounce_back(obstacle_mask)
            else:
                simulator.lbm_core.bounce_back(obstacle_mask)

            # 自适应时间步长
            if params['use_adaptive_tau']:
                try:
                    current_tau = simulator.lbm_core.adaptive_time_stepping(target_cfl=0.1)
                    tau_history.append(current_tau)
                except AttributeError:
                    current_tau = params['tau']
            else:
                current_tau = params['tau']

            # BGK碰撞
            simulator.lbm_core.collision_bgk(current_tau)

            # 流动
            simulator.lbm_core.streaming()

            # 记录收敛指标
            if step % 10 == 0:
                max_velocity = np.max(np.sqrt(simulator.lbm_core.u**2 + simulator.lbm_core.v**2))
                convergence_history.append(max_velocity)

                if len(convergence_history) > 1:
                    oscillation = abs(convergence_history[-1] - convergence_history[-2])
                    velocity_oscillations.append(oscillation)

            # 每500步打印详细收敛状态
            if step % 500 == 0:
                max_velocity = np.max(np.sqrt(simulator.lbm_core.u**2 + simulator.lbm_core.v**2))
                avg_density = np.mean(simulator.lbm_core.rho)
                current_tau_display = tau_history[-1] if tau_history else params['tau']

                # 计算残差
                if prev_u is not None:
                    velocity_residual = np.sqrt(np.mean((simulator.lbm_core.u - prev_u)**2 +
                                                       (simulator.lbm_core.v - prev_v)**2))
                    density_residual = np.sqrt(np.mean((simulator.lbm_core.rho - prev_rho)**2))
                    print(f"    步骤 {step:4d}: 最大速度={max_velocity:.6f}, 平均密度={avg_density:.6f}, "
                          f"速度残差={velocity_residual:.2e}, 密度残差={density_residual:.2e}, tau={current_tau_display:.3f}")
                else:
                    print(f"    步骤 {step:4d}: 最大速度={max_velocity:.6f}, 平均密度={avg_density:.6f}, "
                          f"速度残差=N/A, 密度残差=N/A, tau={current_tau_display:.3f}")

        simulation_time = time.time() - start_time

        # 计算最终性能指标
        final_velocity_field = np.sqrt(simulator.lbm_core.u**2 + simulator.lbm_core.v**2)
        velocity_smoothness = 1 / (1 + np.std(final_velocity_field))

        # 如果使用了FluidSimulator的run_fluid_simulation，则没有手动的convergence_history
        if 'convergence_history' not in locals():
            convergence_history = []
            velocity_oscillations = []
            tau_history = []
            avg_oscillation = 0
        else:
            avg_oscillation = np.mean(velocity_oscillations) if velocity_oscillations else 0

        results = {
            'simulator': simulator,
            'simulation_time': simulation_time,
            'convergence_history': convergence_history,
            'velocity_oscillations': velocity_oscillations,
            'tau_history': tau_history,
            'final_max_velocity': np.max(final_velocity_field),
            'velocity_smoothness': velocity_smoothness,
            'avg_oscillation': avg_oscillation,
            'algorithm_type': 'Enhanced LBM',
            'flow_direction_mode': params['flow_direction_mode']
        }

        print(f"  ✅ 增强LBM完成，耗时: {simulation_time:.3f}秒")
        print(f"    流动方向模式: {params['flow_direction_mode']}")
        print(f"    最大速度: {np.max(final_velocity_field):.6f}")
        print(f"    速度场平滑度: {velocity_smoothness:.6f}")
        return results

    def _generate_sample_path(self, grid_map: np.ndarray, method_type: str) -> List[Tuple[int, int]]:
        """生成示例路径"""
        h, w = grid_map.shape

        # 使用与流场一致的起点终点位置
        # 起点：左上角附近 (与流场入口一致)
        start = (1, 1)  # 左上角
        # 终点：右下角附近 (与流场出口一致)
        end = (h-2, w-2)  # 右下角

        path = [start]
        current = start

        # 简单的路径生成算法
        while current != end and len(path) < h * w:
            # 计算到目标的方向
            dx = 1 if current[0] < end[0] else -1 if current[0] > end[0] else 0
            dy = 1 if current[1] < end[1] else -1 if current[1] > end[1] else 0

            # 根据算法类型调整路径
            if method_type == 'enhanced':
                # 增强算法：更直接的路径
                next_pos = (current[0] + dx, current[1] + dy)
            else:
                # 传统算法：可能有更多弯曲
                if np.random.random() < 0.3:  # 30%概率偏离最优方向
                    dx = np.random.choice([-1, 0, 1])
                    dy = np.random.choice([-1, 0, 1])
                next_pos = (current[0] + dx, current[1] + dy)

            # 检查边界和障碍物
            if (0 <= next_pos[0] < h and 0 <= next_pos[1] < w and
                grid_map[next_pos] == 0):
                current = next_pos
                path.append(current)
            else:
                # 尝试其他方向
                for ddx, ddy in [(0, 1), (1, 0), (0, -1), (-1, 0)]:
                    next_pos = (current[0] + ddx, current[1] + ddy)
                    if (0 <= next_pos[0] < h and 0 <= next_pos[1] < w and
                        grid_map[next_pos] == 0):
                        current = next_pos
                        path.append(current)
                        break
                else:
                    break

        return path




comparison = LBMAlgorithmComparison()

print("🌊 LBM算法对比分析")
print("=" * 50)
# 显示明确的参数设置
print("\n📋 算法参数设置:")
print("传统LBM参数:")
for key, value in comparison.algorithm_params['traditional_lbm'].items():
    print(f"  {key}: {value}")

print("\n增强LBM参数:")
for key, value in comparison.algorithm_params['enhanced_lbm'].items():
    print(f"  {key}: {value}")

print("\n🎯 测试场景参数:")
for key, value in comparison.test_scenario.items():
    print(f"  {key}: {value}")

# 加载真实海冰地图
print("\n1️⃣ 加载真实海冰地图...")
map_data = comparison.load_sea_ice_map()
if map_data is None:
    print("  ❌ 海冰地图加载失败，使用测试网格...")
    grid_map = comparison.create_test_grid()
    # 创建默认的地图数据结构
    map_data = {
        'original_map': grid_map,
        'extended_map': grid_map,
        'extension_size': 0,
        'start_point': (1, 1),
        'end_point': (grid_map.shape[0]-2, grid_map.shape[1]-2),
        'original_shape': grid_map.shape
    }
else:
    grid_map = map_data['extended_map']

print(f"  原始地图尺寸: {map_data['original_shape']}")
print(f"  扩展地图尺寸: {grid_map.shape}")
print(f"  障碍物数量: {np.sum(grid_map == 1)}")
print(f"  流体区域: {np.sum(grid_map == 0)}")
print(f"  起点: {map_data['start_point']}")
print(f"  终点: {map_data['end_point']}")

# 显示边界条件和流动方向
print("\n2️⃣ 显示边界条件和流动方向...")
boundary_visualizer = FluidVisualization()

# 生成两种流动方向模式的可视化
print("  生成垂直模式边界条件可视化...")
perpendicular_viz_path = boundary_visualizer.visualize_boundary_conditions_and_flow_direction(
    map_data,
    save_path=os.path.join(comparison.output_params['output_dir'], "边界条件_垂直模式.png"),
    flow_direction_mode='perpendicular'
)
print(f"    垂直模式可视化已保存: {perpendicular_viz_path}")

print("  生成起点终点模式边界条件可视化...")
point_to_point_viz_path = boundary_visualizer.visualize_boundary_conditions_and_flow_direction(
    map_data,
    save_path=os.path.join(comparison.output_params['output_dir'], "边界条件_起点终点模式.png"),
    flow_direction_mode='point_to_point'
)
print(f"    起点终点模式可视化已保存: {point_to_point_viz_path}")

# 运行传统LBM
print("\n3️⃣ 运行传统LBM算法...")
traditional_results = comparison.run_traditional_lbm(grid_map, map_data)

# 运行增强LBM
print("\n4️⃣ 运行增强LBM算法...")
enhanced_results = comparison.run_enhanced_lbm(grid_map, map_data)

# 生成独立可视化图表
print("\n5️⃣ 生成独立可视化图表...")

from src.visualization import FluidVisualization
visualizer = FluidVisualization()

# 准备数据
trad_planner = traditional_results['simulator']
enh_planner = enhanced_results['simulator']

# 获取扩展参数
h_orig, w_orig = map_data['original_shape']

# 计算实际的扩展大小
# LBM处理后的尺寸
h_lbm, w_lbm = trad_planner.lbm_core.u.shape

# 计算总的扩展大小（包括数据加载时的扩展和LBM处理时的扩展）
total_extension_y = (h_lbm - h_orig) // 2
total_extension_x = (w_lbm - w_orig) // 2

print(f"  调试信息:")
print(f"    原始尺寸: {h_orig}×{w_orig}")
print(f"    LBM处理后尺寸: {h_lbm}×{w_lbm}")
print(f"    总扩展大小: {total_extension_y}×{total_extension_x}")

# 从扩展的数据中提取原始256×256区域
# 传统LBM数据
trad_u_orig = trad_planner.lbm_core.u[total_extension_y:total_extension_y+h_orig, total_extension_x:total_extension_x+w_orig]
trad_v_orig = trad_planner.lbm_core.v[total_extension_y:total_extension_y+h_orig, total_extension_x:total_extension_x+w_orig]
trad_rho_orig = trad_planner.lbm_core.rho[total_extension_y:total_extension_y+h_orig, total_extension_x:total_extension_x+w_orig]

# 增强LBM数据
enh_u_orig = enh_planner.lbm_core.u[total_extension_y:total_extension_y+h_orig, total_extension_x:total_extension_x+w_orig]
enh_v_orig = enh_planner.lbm_core.v[total_extension_y:total_extension_y+h_orig, total_extension_x:total_extension_x+w_orig]
enh_rho_orig = enh_planner.lbm_core.rho[total_extension_y:total_extension_y+h_orig, total_extension_x:total_extension_x+w_orig]

print(f"    提取后数据尺寸: {trad_u_orig.shape}")
print(f"    原始地图尺寸: {map_data['original_map'].shape}")

# 计算涡度（基于原始256×256区域）- 使用正确的涡量公式
from src.utils import FluidAnalyzer
trad_vorticity = FluidAnalyzer.calculate_vorticity(trad_u_orig, trad_v_orig)
enh_vorticity = FluidAnalyzer.calculate_vorticity(enh_u_orig, enh_v_orig)

# 计算速度大小（基于原始256×256区域）
trad_speed = np.sqrt(trad_u_orig**2 + trad_v_orig**2)
enh_speed = np.sqrt(enh_u_orig**2 + enh_v_orig**2)

print(f"    涡度场尺寸: {trad_vorticity.shape}")
print(f"    速度场尺寸: {trad_speed.shape}")

# 准备路径数据（使用原始地图）
original_grid = map_data['original_map']
paths = {
    'Traditional LBM': comparison._generate_sample_path(original_grid, 'traditional'),
    'Enhanced LBM': comparison._generate_sample_path(original_grid, 'enhanced')
}

# 准备性能指标（基于原始256×256区域）
fluid_mask_orig = (original_grid == 0)
metrics = {
    'Traditional LBM': {
        'path_length': len(paths['Traditional LBM']) if paths['Traditional LBM'] else 0,
        'computation_time': traditional_results['simulation_time'],
        'efficiency': traditional_results['velocity_smoothness'],
        'avg_speed': np.mean(trad_speed[fluid_mask_orig]) if np.any(fluid_mask_orig) else 0
    },
    'Enhanced LBM': {
        'path_length': len(paths['Enhanced LBM']) if paths['Enhanced LBM'] else 0,
        'computation_time': enhanced_results['simulation_time'],
        'efficiency': enhanced_results['velocity_smoothness'],
        'avg_speed': np.mean(enh_speed[fluid_mask_orig]) if np.any(fluid_mask_orig) else 0
    }
}

# 创建独立可视化图表目录
individual_viz_dir = os.path.join(comparison.output_params['output_dir'], '单独可视化图表')

# 生成融合对比可视化（单图展示）
print("  生成融合对比可视化...")
fusion_files = visualizer.create_fusion_comparison_visualizations(
    map_data=map_data,
    traditional_data={
        'u': trad_u_orig,
        'v': trad_v_orig,
        'rho': trad_rho_orig,
        'speed': trad_speed,
        'vorticity': trad_vorticity,
        'convergence_history': traditional_results['convergence_history'],
        'metrics': metrics['Traditional LBM']
    },
    enhanced_data={
        'u': enh_u_orig,
        'v': enh_v_orig,
        'rho': enh_rho_orig,
        'speed': enh_speed,
        'vorticity': enh_vorticity,
        'convergence_history': enhanced_results['convergence_history'],
        'metrics': metrics['Enhanced LBM']
    },
    paths=paths,
    output_dir=os.path.join(individual_viz_dir, '融合对比可视化')
)

# 为传统LBM生成独立图表（使用原始256×256数据）
print("  生成传统LBM独立图表...")
trad_files = visualizer.create_individual_visualizations(
    map_data=map_data,
    u=trad_u_orig,
    v=trad_v_orig,
    rho=trad_rho_orig,
    speed=trad_speed,
    vorticity=trad_vorticity,
    paths={'Traditional LBM': paths['Traditional LBM']},
    metrics={'Traditional LBM': metrics['Traditional LBM']},
    convergence_history=traditional_results['convergence_history'],
    output_dir=os.path.join(individual_viz_dir, '传统LBM')
)

# 为增强LBM生成独立图表（使用原始256×256数据）
print("  生成增强LBM独立图表...")
enh_files = visualizer.create_individual_visualizations(
    map_data=map_data,
    u=enh_u_orig,
    v=enh_v_orig,
    rho=enh_rho_orig,
    speed=enh_speed,
    vorticity=enh_vorticity,
    paths={'Enhanced LBM': paths['Enhanced LBM']},
    metrics={'Enhanced LBM': metrics['Enhanced LBM']},
    convergence_history=enhanced_results['convergence_history'],
    output_dir=os.path.join(individual_viz_dir, '增强LBM')
)

print(f"  ✅ 可视化图表已生成:")
print(f"    - 融合对比图表: {len(fusion_files)} 个")
print(f"    - 传统LBM图表: {len(trad_files)} 个")
print(f"    - 增强LBM图表: {len(enh_files)} 个")
print(f"    - 保存位置: {individual_viz_dir}")



# 输出性能总结
print("\n📊 性能对比总结:")
print(f"  传统LBM模拟时间: {traditional_results['simulation_time']:.3f}秒")
print(f"  增强LBM模拟时间: {enhanced_results['simulation_time']:.3f}秒")

# 计算性能改进指标（添加安全检查）
if traditional_results['simulation_time'] > 0:
    time_improvement = (traditional_results['simulation_time'] - enhanced_results['simulation_time']) / traditional_results['simulation_time'] * 100
    print(f"  时间改进: {time_improvement:.1f}%")
else:
    print(f"  时间改进: N/A (传统LBM时间为0)")

if traditional_results['velocity_smoothness'] > 0:
    smoothness_improvement = (enhanced_results['velocity_smoothness'] - traditional_results['velocity_smoothness']) / traditional_results['velocity_smoothness'] * 100
    print(f"  平滑度改进: {smoothness_improvement:.1f}%")
else:
    print(f"  平滑度改进: N/A (传统LBM平滑度为0)")

if traditional_results['avg_oscillation'] > 0:
    oscillation_reduction = (traditional_results['avg_oscillation'] - enhanced_results['avg_oscillation']) / traditional_results['avg_oscillation'] * 100
    print(f"  振荡减少: {oscillation_reduction:.1f}%")
else:
    print(f"  振荡减少: N/A (传统LBM振荡为0)")

# 添加绝对值对比
print(f"\n📈 绝对值对比:")
print(f"  传统LBM最大速度: {traditional_results['final_max_velocity']:.6f}")
print(f"  增强LBM最大速度: {enhanced_results['final_max_velocity']:.6f}")
print(f"  传统LBM平滑度: {traditional_results['velocity_smoothness']:.6f}")
print(f"  增强LBM平滑度: {enhanced_results['velocity_smoothness']:.6f}")
print(f"  传统LBM平均振荡: {traditional_results['avg_oscillation']:.6f}")
print(f"  增强LBM平均振荡: {enhanced_results['avg_oscillation']:.6f}")

# 流动方向模式对比
print(f"\n🧭 流动方向模式对比:")
print(f"  传统LBM流动方向: {traditional_results.get('flow_direction_mode', 'N/A')}")
print(f"  增强LBM流动方向: {enhanced_results.get('flow_direction_mode', 'N/A')}")

print(f"\n✅ 算法对比完成！结果保存在: {comparison.output_params['output_dir']}")





