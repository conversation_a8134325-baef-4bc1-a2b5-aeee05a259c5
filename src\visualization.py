"""
可视化模块 - 提供全面的可视化功能
包含2D/3D可视化、动画、交互式图表等
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
from typing import List, Tuple, Optional, Dict, Any
import time

# 设置matplotlib字体
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12


class FluidVisualization:
    """流体可视化类"""

    def __init__(self):
        """初始化可视化器"""
        self.custom_cmap = self._create_custom_colormap()
        self.figure_count = 0

    def _create_custom_colormap(self) -> LinearSegmentedColormap:
        """创建自定义颜色映射"""
        colors = [(0, 0, 0.8), (0, 0.5, 1), (0.9, 0.9, 1), (1, 0.5, 0), (0.8, 0, 0)]
        return LinearSegmentedColormap.from_list('custom_fluid', colors, N=256)

    def visualize_fluid_fields(self, grid_map: np.ndarray, u: np.ndarray, v: np.ndarray,
                             rho: np.ndarray, speed: np.ndarray, vorticity: np.ndarray,
                             convergence_history: List[float], save_path: Optional[str] = None) -> plt.Figure:
        """
        可视化流体场

        参数:
        grid_map: 网格地图
        u, v: 速度分量
        rho: 密度场
        speed: 速度大小
        vorticity: 涡量场
        convergence_history: 收敛历史
        save_path: 保存路径

        返回:
        matplotlib图形对象
        """
        fig = plt.figure(figsize=(16, 12), dpi=150)

        # 1. 速度大小场
        ax1 = fig.add_subplot(2, 3, 1)
        im1 = ax1.imshow(speed, cmap=self.custom_cmap, origin='upper')
        ax1.contour(grid_map, levels=[0.5], colors='black', linewidths=2, alpha=0.7)
        plt.colorbar(im1, ax=ax1, label='Velocity Magnitude')
        ax1.set_title('Velocity Magnitude Field', fontweight='bold')
        ax1.set_xlabel('X Position')
        ax1.set_ylabel('Y Position')

        # 2. 压力场（密度）
        ax2 = fig.add_subplot(2, 3, 2)
        im2 = ax2.imshow(rho, cmap='RdYlBu_r', origin='upper')
        ax2.contour(grid_map, levels=[0.5], colors='black', linewidths=2, alpha=0.7)
        plt.colorbar(im2, ax=ax2, label='Pressure (Density)')
        ax2.set_title('Pressure Field', fontweight='bold')
        ax2.set_xlabel('X Position')
        ax2.set_ylabel('Y Position')

        # 3. 涡量场
        ax3 = fig.add_subplot(2, 3, 3)
        vorticity_max = np.percentile(np.abs(vorticity), 95)
        im3 = ax3.imshow(vorticity, cmap='seismic', origin='lower',
                        vmin=-vorticity_max, vmax=vorticity_max)
        ax3.contour(grid_map, levels=[0.5], colors='black', linewidths=2, alpha=0.7)
        plt.colorbar(im3, ax=ax3, label='Vorticity')
        ax3.set_title('Vorticity Field', fontweight='bold')
        ax3.set_xlabel('X Position')
        ax3.set_ylabel('Y Position')

        # 4. 流线图
        ax4 = fig.add_subplot(2, 3, 4)
        h, w = grid_map.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))

        # 创建流线，避开障碍物
        mask = (grid_map == 0) & (speed > 1e-6)
        u_masked = np.where(mask, u, 0)
        v_masked = np.where(mask, v, 0)

        strm = ax4.streamplot(x, y, u_masked, v_masked, density=1.5,
                             color=speed, cmap=self.custom_cmap, linewidth=1.5)
        ax4.contourf(grid_map, levels=[0.5, 1.5], colors=['gray'], alpha=0.8)
        plt.colorbar(strm.lines, ax=ax4, label='Velocity Magnitude')
        ax4.set_title('Streamlines', fontweight='bold')
        ax4.set_xlabel('X Position')
        ax4.set_ylabel('Y Position')

        # 5. 速度矢量场
        ax5 = fig.add_subplot(2, 3, 5)
        skip = max(1, min(h, w) // 20)
        x_vec, y_vec = np.meshgrid(np.arange(0, w, skip), np.arange(0, h, skip))
        u_vec = u[::skip, ::skip]
        v_vec = v[::skip, ::skip]
        speed_vec = speed[::skip, ::skip]

        quiver = ax5.quiver(x_vec, y_vec, u_vec, v_vec, speed_vec,
                           cmap=self.custom_cmap, scale=20, alpha=0.8)
        ax5.contourf(grid_map, levels=[0.5, 1.5], colors=['gray'], alpha=0.6)
        plt.colorbar(quiver, ax=ax5, label='Velocity Magnitude')
        ax5.set_title('Velocity Vectors', fontweight='bold')
        ax5.set_xlabel('X Position')
        ax5.set_ylabel('Y Position')

        # 6. 收敛历史
        ax6 = fig.add_subplot(2, 3, 6)
        if convergence_history:
            ax6.semilogy(convergence_history, 'b-', linewidth=2)
            ax6.set_xlabel('Iteration')
            ax6.set_ylabel('Velocity Change (log scale)')
            ax6.set_title('Convergence History', fontweight='bold')
            ax6.grid(True, alpha=0.3)

            # 添加收敛信息
            final_error = convergence_history[-1] if convergence_history else 0
            ax6.text(0.05, 0.95, f'Final Error: {final_error:.2e}',
                    transform=ax6.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')

        return fig

    def visualize_3d_surface(self, field: np.ndarray, title: str = "3D Surface",
                           colormap: str = 'viridis') -> plt.Figure:
        """
        3D表面可视化

        参数:
        field: 要可视化的场
        title: 图标题
        colormap: 颜色映射

        返回:
        matplotlib图形对象
        """
        fig = plt.figure(figsize=(12, 8), dpi=150)
        ax = fig.add_subplot(111, projection='3d')

        h, w = field.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))

        # 创建3D表面
        surf = ax.plot_surface(x, y, field, cmap=colormap, alpha=0.9,
                              linewidth=0, antialiased=True)

        # 添加等高线投影
        ax.contour(x, y, field, zdir='z', offset=np.min(field),
                  cmap=colormap, alpha=0.5)

        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        ax.set_zlabel('Field Value')
        ax.set_title(title, fontweight='bold')

        # 添加颜色条
        fig.colorbar(surf, ax=ax, shrink=0.5, aspect=20)

        return fig

    def visualize_path_comparison(self, grid_map: np.ndarray, speed_field: np.ndarray,
                                paths: Dict[str, List[Tuple[int, int]]],
                                metrics: Dict[str, Dict[str, float]],
                                save_path: Optional[str] = None) -> plt.Figure:
        """
        路径比较可视化

        参数:
        grid_map: 网格地图
        speed_field: 速度场
        paths: 路径字典 {算法名: 路径}
        metrics: 性能指标字典
        save_path: 保存路径

        返回:
        matplotlib图形对象
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8), dpi=150)

        # 左图：路径可视化
        im = ax1.imshow(speed_field, cmap=self.custom_cmap, origin='upper', alpha=0.7)
        ax1.contourf(grid_map, levels=[0.5, 1.5], colors=['gray'], alpha=0.8)

        # 绘制不同算法的路径
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        line_styles = ['-', '--', '-.', ':', '-']

        for i, (name, path) in enumerate(paths.items()):
            if path:
                path_array = np.array(path)
                ax1.plot(path_array[:, 1], path_array[:, 0],
                        color=colors[i % len(colors)],
                        linestyle=line_styles[i % len(line_styles)],
                        linewidth=3, label=name, alpha=0.9)

                # 标记起点和终点
                ax1.scatter(path_array[0, 1], path_array[0, 0],
                           color=colors[i % len(colors)], s=100, marker='o',
                           edgecolors='black', linewidth=2)
                ax1.scatter(path_array[-1, 1], path_array[-1, 0],
                           color=colors[i % len(colors)], s=100, marker='s',
                           edgecolors='black', linewidth=2)

        ax1.set_title('Path Comparison', fontweight='bold', fontsize=14)
        ax1.set_xlabel('X Position')
        ax1.set_ylabel('Y Position')
        ax1.legend(loc='upper right')
        plt.colorbar(im, ax=ax1, label='Velocity Magnitude')

        # 右图：性能指标比较
        if metrics:
            # 选择数值型指标进行比较
            numeric_metrics = ['path_length', 'euclidean_length', 'computation_time', 'efficiency']
            available_metrics = []

            # 检查哪些指标在所有算法中都存在
            for metric in numeric_metrics:
                if all(metric in alg_metrics for alg_metrics in metrics.values()):
                    available_metrics.append(metric)

            if available_metrics:
                algorithm_names = list(metrics.keys())
                x = np.arange(len(available_metrics))
                width = 0.8 / len(algorithm_names)

                for i, (alg_name, alg_metrics) in enumerate(metrics.items()):
                    values = [alg_metrics.get(metric, 0) for metric in available_metrics]
                    ax2.bar(x + i * width, values, width, label=alg_name,
                           color=colors[i % len(colors)], alpha=0.8)

                ax2.set_xlabel('Metrics')
                ax2.set_ylabel('Values')
                ax2.set_title('Performance Comparison', fontweight='bold', fontsize=14)
                ax2.set_xticks(x + width * (len(algorithm_names) - 1) / 2)
                ax2.set_xticklabels(available_metrics, rotation=45)
                ax2.legend()
                ax2.grid(True, alpha=0.3)
            else:
                ax2.text(0.5, 0.5, 'No comparable metrics available',
                        ha='center', va='center', transform=ax2.transAxes)

        plt.tight_layout()

        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')

        return fig

    def create_animation(self, field_sequence: List[np.ndarray],
                        title: str = "Field Animation",
                        interval: int = 100, save_path: Optional[str] = None) -> FuncAnimation:
        """
        创建场动画

        参数:
        field_sequence: 场序列
        title: 动画标题
        interval: 帧间隔（毫秒）
        save_path: 保存路径

        返回:
        动画对象
        """
        fig, ax = plt.subplots(figsize=(10, 8), dpi=150)

        # 初始化
        vmin = min(np.min(field) for field in field_sequence)
        vmax = max(np.max(field) for field in field_sequence)

        im = ax.imshow(field_sequence[0], cmap=self.custom_cmap,
                      vmin=vmin, vmax=vmax, origin='upper')
        plt.colorbar(im, ax=ax)
        ax.set_title(title, fontweight='bold')

        def animate(frame):
            im.set_array(field_sequence[frame])
            ax.set_title(f'{title} - Frame {frame}', fontweight='bold')
            return [im]

        anim = FuncAnimation(fig, animate, frames=len(field_sequence),
                           interval=interval, blit=True, repeat=True)

        if save_path:
            anim.save(save_path, writer='pillow', fps=10)

        return anim

    def plot_performance_metrics(self, metrics_data: Dict[str, Dict[str, float]],
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制性能指标图表

        参数:
        metrics_data: 指标数据 {算法名: {指标名: 值}}
        save_path: 保存路径

        返回:
        matplotlib图形对象
        """
        fig, axes = plt.subplots(2, 2, figsize=(14, 10), dpi=150)
        axes = axes.flatten()

        algorithms = list(metrics_data.keys())
        metrics = list(next(iter(metrics_data.values())).keys())

        colors = plt.cm.Set3(np.linspace(0, 1, len(algorithms)))

        for i, metric in enumerate(metrics[:4]):  # 最多显示4个指标
            ax = axes[i]
            values = [metrics_data[alg][metric] for alg in algorithms]

            bars = ax.bar(algorithms, values, color=colors, alpha=0.8)
            ax.set_title(f'{metric}', fontweight='bold')
            ax.set_ylabel('Value')

            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.3f}', ha='center', va='bottom')

            ax.grid(True, alpha=0.3)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        plt.tight_layout()

        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')

        return fig

    def visualize_grid_with_boundary(self, grid_map: np.ndarray,
                                   inlet_pos: Optional[Tuple[int, int]] = None,
                                   outlet_pos: Optional[Tuple[int, int]] = None,
                                   save_path: Optional[str] = None) -> plt.Figure:
        """
        可视化带边界的网格

        参数:
        grid_map: 网格地图
        inlet_pos: 入口位置
        outlet_pos: 出口位置
        save_path: 保存路径

        返回:
        matplotlib图形对象
        """
        fig, ax = plt.subplots(figsize=(10, 8), dpi=150)

        # 显示网格
        im = ax.imshow(grid_map, cmap='gray_r', origin='upper')

        # 标记入口和出口
        if inlet_pos:
            ax.scatter(inlet_pos[1], inlet_pos[0], color='green', s=200,
                      marker='o', label='Inlet', edgecolors='black', linewidth=2)

        if outlet_pos:
            ax.scatter(outlet_pos[1], outlet_pos[0], color='red', s=200,
                      marker='s', label='Outlet', edgecolors='black', linewidth=2)

        ax.set_title('Grid Map with Boundaries', fontweight='bold', fontsize=14)
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')

        if inlet_pos or outlet_pos:
            ax.legend()

        plt.colorbar(im, ax=ax, label='Obstacle (1) / Fluid (0)')

        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')

        return fig

    def create_individual_visualizations(self, map_data: Dict, u: np.ndarray, v: np.ndarray,
                                       rho: np.ndarray, speed: np.ndarray, vorticity: np.ndarray,
                                       paths: Dict[str, List[Tuple[int, int]]],
                                       metrics: Dict[str, Dict[str, float]],
                                       convergence_history: List[float],
                                       output_dir: str = "单独可视化图表") -> Dict[str, str]:
        """
        创建独立的单图可视化，每个图表专注一个分析维度

        参数:
        map_data: 地图数据字典，包含原始地图、扩展地图、起点终点等信息
        u, v: 速度分量
        rho: 密度场
        speed: 速度大小
        vorticity: 涡量场
        paths: 路径字典
        metrics: 性能指标
        convergence_history: 收敛历史
        output_dir: 输出目录

        返回:
        生成的文件路径字典
        """
        import os
        os.makedirs(output_dir, exist_ok=True)

        generated_files = {}

        # 提取地图信息
        original_map = map_data['original_map']  # 256x256原始地图
        extended_map = map_data['extended_map']  # 扩展地图用于LBM计算
        extension_size = map_data['extension_size']
        start_point = map_data['start_point']
        end_point = map_data['end_point']

        # 直接使用传入的数据（已经在主程序中正确提取了256×256区域）
        u_original = u
        v_original = v
        rho_original = rho
        speed_original = speed
        vorticity_original = vorticity

        # 起点终点坐标已经是基于扩展地图的，需要转换到原始地图坐标系
        # 但是传入的数据已经是256×256的原始区域，所以起点终点也需要相应调整
        if extension_size > 0:
            # 计算在原始256×256地图中的相对位置
            start_original = (start_point[0] - extension_size, start_point[1] - extension_size)
            end_original = (end_point[0] - extension_size, end_point[1] - extension_size)

            # 确保坐标在有效范围内
            h_orig, w_orig = original_map.shape
            start_original = (max(0, min(h_orig-1, start_original[0])),
                            max(0, min(w_orig-1, start_original[1])))
            end_original = (max(0, min(h_orig-1, end_original[0])),
                          max(0, min(w_orig-1, end_original[1])))
        else:
            start_original = start_point
            end_original = end_point

        print("🎨 生成独立可视化图表...")
        print(f"  原始地图尺寸: {original_map.shape}")
        print(f"  扩展地图起点: {start_point}, 终点: {end_point}")
        print(f"  原始地图起点: {start_original}, 终点: {end_original}")

        # 1. 速度场分布图
        print("  生成速度场分布图...")
        generated_files['速度场分布图'] = self._create_velocity_field_plot(
            original_map, speed_original, u_original, v_original, start_original, end_original,
            os.path.join(output_dir, "速度场分布图.png"))

        # 2. 压力场三维展示
        print("  生成压力场三维展示...")
        generated_files['压力场三维展示'] = self._create_3d_pressure_plot(
            rho_original, original_map, os.path.join(output_dir, "压力场三维展示.png"))

        # 3. 涡度场分析图
        print("  生成涡度场分析图...")
        generated_files['涡度场分析图'] = self._create_vorticity_analysis_plot(
            vorticity_original, original_map, os.path.join(output_dir, "涡度场分析图.png"))

        # 4. 流线分布图
        print("  生成流线分布图...")
        generated_files['流线分布图'] = self._create_streamline_plot(
            original_map, u_original, v_original, speed_original, start_original, end_original,
            os.path.join(output_dir, "流线分布图.png"))

        # 5. 收敛性能对比图
        print("  生成收敛性能对比图...")
        generated_files['收敛性能对比图'] = self._create_convergence_plot(
            convergence_history, os.path.join(output_dir, "收敛性能对比图.png"))

        # 6. 路径规划效果对比图
        print("  生成路径规划效果对比图...")
        generated_files['路径规划效果对比图'] = self._create_path_comparison_plot(
            original_map, speed_original, paths, metrics, start_original, end_original,
            os.path.join(output_dir, "路径规划效果对比图.png"))

        # 7. 速度分布统计图
        print("  生成速度分布统计图...")
        generated_files['速度分布统计图'] = self._create_velocity_statistics_plot(
            speed_original, original_map, os.path.join(output_dir, "速度分布统计图.png"))

        # 8. 压力梯度分析图
        print("  生成压力梯度分析图...")
        generated_files['压力梯度分析图'] = self._create_pressure_gradient_plot(
            rho_original, original_map, os.path.join(output_dir, "压力梯度分析图.png"))

        # 9. 动能分布分析图
        print("  生成动能分布分析图...")
        generated_files['动能分布分析图'] = self._create_kinetic_energy_plot(
            u_original, v_original, rho_original, original_map, os.path.join(output_dir, "动能分布分析图.png"))

        # 10. 性能指标雷达图
        print("  生成性能指标雷达图...")
        generated_files['性能指标雷达图'] = self._create_performance_radar_plot(
            metrics, os.path.join(output_dir, "性能指标雷达图.png"))

        # 11. 扩展边界地图展示图
        print("  生成扩展边界地图展示图...")
        generated_files['扩展边界地图展示图'] = self._create_extended_boundary_map_plot(
            map_data, os.path.join(output_dir, "扩展边界地图展示图.png"))

        print(f"  ✅ 共生成 {len(generated_files)} 个独立可视化图表")
        print(f"  📁 保存位置: {output_dir}")

        return generated_files

    def create_fusion_comparison_visualizations(self, map_data: Dict, traditional_data: Dict,
                                               enhanced_data: Dict, paths: Dict,
                                               output_dir: str) -> Dict[str, str]:
        """
        创建融合对比可视化图表（单图展示两种算法的对比）

        参数:
        map_data: 地图数据
        traditional_data: 传统LBM数据
        enhanced_data: 增强LBM数据
        paths: 路径数据
        output_dir: 输出目录

        返回:
        generated_files: 生成的文件字典
        """
        os.makedirs(output_dir, exist_ok=True)
        generated_files = {}

        # 提取地图信息
        original_map = map_data['original_map']
        extension_size = map_data['extension_size']
        start_point = map_data['start_point']
        end_point = map_data['end_point']

        # 转换起点终点坐标到原始地图坐标系
        if extension_size > 0:
            start_original = (max(0, min(original_map.shape[0]-1, start_point[0] - extension_size)),
                            max(0, min(original_map.shape[1]-1, start_point[1] - extension_size)))
            end_original = (max(0, min(original_map.shape[0]-1, end_point[0] - extension_size)),
                          max(0, min(original_map.shape[1]-1, end_point[1] - extension_size)))
        else:
            start_original = start_point
            end_original = end_point

        print("🎨 生成融合对比可视化图表...")
        print(f"  原始地图尺寸: {original_map.shape}")
        print(f"  起点: {start_original}, 终点: {end_original}")

        # 1. 速度场对比图
        print("  生成速度场对比图...")
        generated_files['速度场对比图'] = self._create_velocity_field_comparison(
            original_map, traditional_data['speed'], enhanced_data['speed'],
            start_original, end_original, os.path.join(output_dir, "速度场对比图.png"))

        # 2. 涡度场对比图
        print("  生成涡度场对比图...")
        generated_files['涡度场对比图'] = self._create_vorticity_comparison(
            original_map, traditional_data['vorticity'], enhanced_data['vorticity'],
            start_original, end_original, os.path.join(output_dir, "涡度场对比图.png"))

        # 3. 压力场对比图
        print("  生成压力场对比图...")
        generated_files['压力场对比图'] = self._create_pressure_comparison(
            original_map, traditional_data['rho'], enhanced_data['rho'],
            start_original, end_original, os.path.join(output_dir, "压力场对比图.png"))

        # 4. 收敛性能对比图
        print("  生成收敛性能对比图...")
        generated_files['收敛性能对比图'] = self._create_convergence_comparison(
            traditional_data['convergence_history'], enhanced_data['convergence_history'],
            os.path.join(output_dir, "收敛性能对比图.png"))

        # 5. 性能指标对比图
        print("  生成性能指标对比图...")
        generated_files['性能指标对比图'] = self._create_metrics_comparison(
            traditional_data['metrics'], enhanced_data['metrics'],
            os.path.join(output_dir, "性能指标对比图.png"))

        # 6. 路径规划效果对比图
        print("  生成路径规划效果对比图...")
        generated_files['路径规划效果对比图'] = self._create_path_comparison_plot(
            original_map, enhanced_data['speed'], paths,
            {'Traditional LBM': traditional_data['metrics'], 'Enhanced LBM': enhanced_data['metrics']},
            start_original, end_original, os.path.join(output_dir, "路径规划效果对比图.png"))

        print(f"  ✅ 共生成 {len(generated_files)} 个融合对比图表")
        print(f"  📁 保存位置: {output_dir}")

        return generated_files

    def _create_velocity_field_plot(self, grid_map: np.ndarray, speed: np.ndarray,
                                   u: np.ndarray, v: np.ndarray, start_point: Tuple[int, int],
                                   end_point: Tuple[int, int], save_path: str) -> str:
        """创建速度场分布图"""
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 使用imshow显示速度场背景，确保正确的方向
        im = ax.imshow(speed, cmap='viridis', origin='upper', alpha=0.8)

        # 速度场等高线图 - 翻转y轴以匹配imshow的origin='upper'
        h, w = speed.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))
        speed_max = np.max(speed)
        speed_min = np.min(speed)
        if speed_max > speed_min:
            levels = np.linspace(speed_min, speed_max, 20)
        else:
            levels = np.linspace(0, max(speed_max, 1e-6), 20)
        contour_filled = ax.contour(x, y, speed, levels=levels, colors='white', linewidths=0.5, alpha=0.7)

        # 障碍物边界
        ax.contour(x, y, grid_map, levels=[0.5], colors='black', linewidths=2)

        # 添加流线
        h, w = grid_map.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))
        mask = (grid_map == 0) & (speed > 1e-6)
        u_masked = np.where(mask, u, 0)
        v_masked = np.where(mask, v, 0)

        try:
            ax.streamplot(x, y, u_masked, v_masked, density=1.5, color='white',
                         linewidth=1, alpha=0.6, arrowsize=1.5)
        except:
            pass  # 如果streamplot失败，跳过

        # 设置标题和标签
        ax.set_title('Velocity Field Distribution',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(contour_filled, ax=ax, shrink=0.8)
        cbar.set_label('Velocity Magnitude (m/s)', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)

        # 添加起点和终点标记
        if start_point and end_point:
            # 起点 (绿色圆圈)
            ax.scatter(start_point[1], start_point[0], color='lime', s=200, marker='o',
                      edgecolors='black', linewidth=3, label='Start', zorder=10)
            ax.text(start_point[1], start_point[0]+5, 'Start', ha='center', va='bottom',
                   fontsize=12, fontweight='bold', color='white',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.8))

            # 终点 (红色方块)
            ax.scatter(end_point[1], end_point[0], color='red', s=200, marker='s',
                      edgecolors='black', linewidth=3, label='End', zorder=10)
            ax.text(end_point[1], end_point[0]+5, 'End', ha='center', va='bottom',
                   fontsize=12, fontweight='bold', color='white',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.8))

        # 添加统计信息
        max_speed = np.max(speed)
        avg_speed = np.mean(speed[grid_map == 0])
        info_text = f'Max Speed: {max_speed:.4f} m/s\nAvg Speed: {avg_speed:.4f} m/s'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_3d_pressure_plot(self, rho: np.ndarray, grid_map: np.ndarray, save_path: str) -> str:
        """创建压力场三维展示"""
        fig = plt.figure(figsize=(14, 10), dpi=500)
        ax = fig.add_subplot(111, projection='3d')

        h, w = rho.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))

        # 只显示流体区域的压力
        rho_masked = np.where(grid_map == 0, rho, np.nan)

        # 3D表面图
        surf = ax.plot_surface(x, y, rho_masked, cmap='RdYlBu_r', alpha=0.8,
                              linewidth=0, antialiased=True, shade=True)

        # 添加等高线投影
        ax.contour(x, y, rho_masked, zdir='z', offset=np.nanmin(rho_masked),
                  cmap='RdYlBu_r', alpha=0.5, levels=15)

        # 设置标题和标签
        ax.set_title('3D Pressure Field Visualization',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=12, labelpad=10)
        ax.set_ylabel('Y Position', fontsize=12, labelpad=10)
        ax.set_zlabel('Pressure (Pa)', fontsize=12, labelpad=10)

        # 颜色条
        cbar = fig.colorbar(surf, ax=ax, shrink=0.6, aspect=20, pad=0.1)
        cbar.set_label('Pressure (Pa)', fontsize=12, fontweight='bold')

        # 设置视角
        ax.view_init(elev=30, azim=45)

        # 添加统计信息
        pressure_range = np.nanmax(rho_masked) - np.nanmin(rho_masked)
        info_text = f'Pressure Range: {pressure_range:.6f} Pa'
        ax.text2D(0.02, 0.98, info_text, transform=ax.transAxes,
                 fontsize=11, bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_vorticity_analysis_plot(self, vorticity: np.ndarray, grid_map: np.ndarray, save_path: str) -> str:
        """创建涡度场分析图"""
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 计算涡度范围
        vorticity_max = np.percentile(np.abs(vorticity), 95)
        vorticity_min = np.min(vorticity)
        vorticity_max_val = np.max(vorticity)

        # 确保等高线级别是递增的
        if vorticity_max_val > vorticity_min:
            levels = np.linspace(vorticity_min, vorticity_max_val, 21)
        else:
            # 如果涡度场为零或常数，使用对称范围
            vorticity_max = max(vorticity_max, 1e-6)
            levels = np.linspace(-vorticity_max, vorticity_max, 21)

        # 使用imshow显示涡度场背景，确保正确的方向
        im = ax.imshow(vorticity, cmap='seismic', origin='upper', alpha=0.8, vmin=levels[0], vmax=levels[-1])

        # 涡度场等高线图 - 使用坐标网格确保正确方向
        h, w = vorticity.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))
        contour_lines = ax.contour(x, y, vorticity, levels=levels[::2], colors='black', linewidths=0.5, alpha=0.5)

        # 障碍物边界
        ax.contour(x, y, grid_map, levels=[0.5], colors='white', linewidths=2)

        # 设置标题和标签
        ax.set_title('Vorticity Field Analysis',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Vorticity (1/s)', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)

        # 添加统计信息
        max_vorticity = np.max(np.abs(vorticity))
        avg_vorticity = np.mean(np.abs(vorticity[grid_map == 0]))
        info_text = f'Max |Vorticity|: {max_vorticity:.6f} 1/s\nAvg |Vorticity|: {avg_vorticity:.6f} 1/s'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_streamline_plot(self, grid_map: np.ndarray, u: np.ndarray, v: np.ndarray,
                               speed: np.ndarray, start_point: Tuple[int, int],
                               end_point: Tuple[int, int], save_path: str) -> str:
        """创建流线分布图"""
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        h, w = grid_map.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))

        # 创建掩码，避开障碍物
        mask = (grid_map == 0) & (speed > 1e-6)
        u_masked = np.where(mask, u, 0)
        v_masked = np.where(mask, v, 0)

        # 背景速度场
        im = ax.imshow(speed, cmap='viridis', origin='upper', alpha=0.6)

        # 流线图
        try:
            strm = ax.streamplot(x, y, u_masked, v_masked, density=2.0,
                               color=speed, cmap='plasma', linewidth=2, arrowsize=2)
        except:
            # 如果streamplot失败，使用简化版本
            strm = ax.streamplot(x, y, u_masked, v_masked, density=1.0,
                               color='white', linewidth=1.5)

        # 障碍物边界
        ax.contour(grid_map, levels=[0.5], colors='black', linewidths=3)

        # 设置标题和标签
        ax.set_title('Streamline Distribution',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 添加起点和终点标记
        if start_point and end_point:
            # 起点 (绿色圆圈)
            ax.scatter(start_point[1], start_point[0], color='lime', s=200, marker='o',
                      edgecolors='black', linewidth=3, label='Start', zorder=10)

            # 终点 (红色方块)
            ax.scatter(end_point[1], end_point[0], color='red', s=200, marker='s',
                      edgecolors='black', linewidth=3, label='End', zorder=10)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Velocity Magnitude (m/s)', fontsize=12, fontweight='bold')

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_convergence_plot(self, convergence_history: List[float], save_path: str) -> str:
        """创建收敛性能对比图"""
        fig, ax = plt.subplots(figsize=(12, 8), dpi=500)

        if not convergence_history:
            ax.text(0.5, 0.5, '无收敛数据\nNo Convergence Data',
                   ha='center', va='center', transform=ax.transAxes, fontsize=16)
            ax.set_title('收敛性能对比图\nConvergence Performance', fontsize=16, fontweight='bold')
            fig.savefig(save_path, dpi=500, bbox_inches='tight')
            plt.close()
            return save_path

        iterations = np.arange(len(convergence_history))

        # 主收敛曲线
        ax.semilogy(iterations, convergence_history, 'b-', linewidth=3,
                   label='Convergence Error', alpha=0.8)

        # 添加趋势线
        if len(convergence_history) > 10:
            # 计算后半段的趋势
            mid_point = len(convergence_history) // 2
            x_trend = iterations[mid_point:]
            y_trend = np.log(convergence_history[mid_point:])

            # 线性拟合
            z = np.polyfit(x_trend, y_trend, 1)
            p = np.poly1d(z)
            trend_line = np.exp(p(x_trend))

            ax.semilogy(x_trend, trend_line, 'r--', linewidth=2,
                       label=f'Trend (slope: {z[0]:.4f})', alpha=0.7)

        # 标记关键点
        final_error = convergence_history[-1]
        initial_error = convergence_history[0]

        ax.scatter([0], [initial_error], color='green', s=100,
                  label=f'Initial: {initial_error:.2e}', zorder=5)
        ax.scatter([len(convergence_history)-1], [final_error], color='red', s=100,
                  label=f'Final: {final_error:.2e}', zorder=5)

        # 设置标题和标签
        ax.set_title('Convergence Performance Analysis',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Iteration', fontsize=14)
        ax.set_ylabel('Error (log scale)', fontsize=14)
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)

        # 添加统计信息
        convergence_rate = (initial_error - final_error) / initial_error * 100
        info_text = f'Convergence Rate: {convergence_rate:.2f}%\nIterations: {len(convergence_history)}'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_path_comparison_plot(self, grid_map: np.ndarray, speed: np.ndarray,
                                   paths: Dict[str, List[Tuple[int, int]]],
                                   metrics: Dict[str, Dict[str, float]],
                                   start_point: Tuple[int, int], end_point: Tuple[int, int],
                                   save_path: str) -> str:
        """创建路径规划效果对比图"""
        fig, ax = plt.subplots(figsize=(14, 10), dpi=500)

        # 背景速度场
        im = ax.imshow(speed, cmap='viridis', origin='upper', alpha=0.7)

        # 障碍物
        ax.contourf(grid_map, levels=[0.5, 1.5], colors=['gray'], alpha=0.8)

        # 绘制路径
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'cyan', 'magenta']
        line_styles = ['-', '--', '-.', ':', '-', '--', '-.']

        legend_elements = []

        for i, (name, path) in enumerate(paths.items()):
            if path and len(path) > 1:
                path_array = np.array(path)
                color = colors[i % len(colors)]
                style = line_styles[i % len(line_styles)]

                # 绘制路径
                line = ax.plot(path_array[:, 1], path_array[:, 0], color=color,
                              linestyle=style, linewidth=4, label=name, alpha=0.9)[0]
                legend_elements.append(line)

                # 标记起点和终点
                ax.scatter(path_array[0, 1], path_array[0, 0], color=color,
                          s=150, marker='o', edgecolors='black', linewidth=2)
                ax.scatter(path_array[-1, 1], path_array[-1, 0], color=color,
                          s=150, marker='s', edgecolors='black', linewidth=2)

        # 设置标题和标签
        ax.set_title('Path Planning Comparison',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 添加起点和终点标记
        if start_point and end_point:
            # 起点 (绿色圆圈)
            ax.scatter(start_point[1], start_point[0], color='lime', s=300, marker='o',
                      edgecolors='black', linewidth=4, label='Start', zorder=15)
            ax.text(start_point[1], start_point[0]+8, 'Start', ha='center', va='bottom',
                   fontsize=14, fontweight='bold', color='white',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='green', alpha=0.9))

            # 终点 (红色方块)
            ax.scatter(end_point[1], end_point[0], color='red', s=300, marker='s',
                      edgecolors='black', linewidth=4, label='End', zorder=15)
            ax.text(end_point[1], end_point[0]+8, 'End', ha='center', va='bottom',
                   fontsize=14, fontweight='bold', color='white',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='red', alpha=0.9))

        # 图例
        if legend_elements:
            ax.legend(handles=legend_elements, bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Velocity Magnitude (m/s)', fontsize=12, fontweight='bold')

        # 添加路径统计信息
        if paths:
            path_info = []
            for name, path in paths.items():
                if path:
                    length = len(path)
                    path_info.append(f'{name}: {length} points')

            info_text = 'Path Lengths:\n' + '\n'.join(path_info)
            ax.text(0.02, 0.02, info_text, transform=ax.transAxes,
                   verticalalignment='bottom', fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_velocity_statistics_plot(self, speed: np.ndarray, grid_map: np.ndarray, save_path: str) -> str:
        """创建速度分布统计图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8), dpi=500)

        # 提取流体区域的速度
        speed_fluid = speed[grid_map == 0]

        # 左图：速度分布直方图
        n, bins, patches = ax1.hist(speed_fluid, bins=50, alpha=0.7, color='skyblue',
                                   edgecolor='black', density=True)

        # 添加统计线
        mean_speed = np.mean(speed_fluid)
        median_speed = np.median(speed_fluid)
        std_speed = np.std(speed_fluid)

        ax1.axvline(mean_speed, color='red', linestyle='--', linewidth=2,
                   label=f'Mean: {mean_speed:.4f} m/s')
        ax1.axvline(median_speed, color='green', linestyle='--', linewidth=2,
                   label=f'Median: {median_speed:.4f} m/s')
        ax1.axvline(mean_speed + std_speed, color='orange', linestyle=':', linewidth=2,
                   label=f'Mean + σ: {mean_speed + std_speed:.4f} m/s')
        ax1.axvline(mean_speed - std_speed, color='orange', linestyle=':', linewidth=2,
                   label=f'Mean - σ: {mean_speed - std_speed:.4f} m/s')

        ax1.set_title('Velocity Distribution Histogram', fontweight='bold')
        ax1.set_xlabel('Velocity Magnitude (m/s)', fontsize=12)
        ax1.set_ylabel('Probability Density', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 右图：累积分布函数
        sorted_speeds = np.sort(speed_fluid)
        cumulative = np.arange(1, len(sorted_speeds) + 1) / len(sorted_speeds)

        ax2.plot(sorted_speeds, cumulative, 'b-', linewidth=2, label='CDF')
        ax2.axhline(0.5, color='red', linestyle='--', alpha=0.7, label='50th Percentile')
        ax2.axhline(0.95, color='orange', linestyle='--', alpha=0.7, label='95th Percentile')

        ax2.set_title('Cumulative Distribution Function', fontweight='bold')
        ax2.set_xlabel('Velocity Magnitude (m/s)', fontsize=12)
        ax2.set_ylabel('Cumulative Probability', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 添加统计信息
        stats_text = f"""统计信息 Statistics:
        样本数 Samples: {len(speed_fluid)}
        最小值 Min: {np.min(speed_fluid):.6f} m/s
        最大值 Max: {np.max(speed_fluid):.6f} m/s
        均值 Mean: {mean_speed:.6f} m/s
        中位数 Median: {median_speed:.6f} m/s
        标准差 Std: {std_speed:.6f} m/s
        偏度 Skewness: {((speed_fluid - mean_speed) ** 3).mean() / std_speed ** 3:.3f}
        """

        fig.text(0.02, 0.02, stats_text, fontsize=10, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_pressure_gradient_plot(self, rho: np.ndarray, grid_map: np.ndarray, save_path: str) -> str:
        """创建压力梯度分析图"""
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 计算压力梯度
        grad_x = np.gradient(rho, axis=1)
        grad_y = np.gradient(rho, axis=0)
        grad_magnitude = np.sqrt(grad_x**2 + grad_y**2)

        # 只显示流体区域的梯度
        grad_masked = np.where(grid_map == 0, grad_magnitude, np.nan)

        # 梯度大小等高线图
        grad_min = np.nanmin(grad_magnitude)
        grad_max = np.nanpercentile(grad_magnitude, 95)

        # 确保等高线级别是递增的
        if grad_max > grad_min and not np.isnan(grad_max) and not np.isnan(grad_min):
            levels = np.linspace(grad_min, grad_max, 20)
        else:
            # 如果梯度场为零或常数，使用默认范围
            levels = np.linspace(0, max(grad_max, 1e-6), 20)

        # 使用imshow显示压力梯度背景，确保正确的方向
        im = ax.imshow(grad_masked, cmap='hot', origin='upper', alpha=0.8, vmin=levels[0], vmax=levels[-1])

        # 障碍物边界 - 使用坐标网格确保正确方向
        h, w = grid_map.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))
        ax.contour(x, y, grid_map, levels=[0.5], colors='black', linewidths=2)

        # 添加梯度矢量
        h, w = rho.shape
        skip = max(1, min(h, w) // 15)
        x, y = np.meshgrid(np.arange(0, w, skip), np.arange(0, h, skip))

        grad_x_sub = grad_x[::skip, ::skip]
        grad_y_sub = grad_y[::skip, ::skip]
        grad_mag_sub = grad_magnitude[::skip, ::skip]

        # 归一化梯度矢量
        norm = np.sqrt(grad_x_sub**2 + grad_y_sub**2)
        norm[norm == 0] = 1  # 避免除零
        grad_x_norm = grad_x_sub / norm
        grad_y_norm = grad_y_sub / norm

        ax.quiver(x, y, grad_x_norm, grad_y_norm, grad_mag_sub,
                 cmap='plasma', scale=30, alpha=0.7, width=0.003)

        # 设置标题和标签
        ax.set_title('Pressure Gradient Analysis',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('|∇P| (Pa/m)', fontsize=12, fontweight='bold')

        # 添加统计信息
        max_grad = np.nanmax(grad_magnitude)
        avg_grad = np.nanmean(grad_magnitude[grid_map == 0])
        info_text = f'Max |∇P|: {max_grad:.6f} Pa/m\nAvg |∇P|: {avg_grad:.6f} Pa/m'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_kinetic_energy_plot(self, u: np.ndarray, v: np.ndarray, rho: np.ndarray,
                                   grid_map: np.ndarray, save_path: str) -> str:
        """创建动能分布分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8), dpi=500)

        # 计算动能
        kinetic_energy = 0.5 * rho * (u**2 + v**2)
        ke_fluid = kinetic_energy[grid_map == 0]

        # 左图：动能场分布
        ke_masked = np.where(grid_map == 0, kinetic_energy, np.nan)
        im = ax1.imshow(ke_masked, cmap='plasma', origin='upper')
        ax1.contour(grid_map, levels=[0.5], colors='white', linewidths=2)

        ax1.set_title('Kinetic Energy Field', fontweight='bold')
        ax1.set_xlabel('X Position', fontsize=12)
        ax1.set_ylabel('Y Position', fontsize=12)

        cbar1 = plt.colorbar(im, ax=ax1, shrink=0.8)
        cbar1.set_label('Kinetic Energy (J/m³)', fontsize=11)

        # 右图：动能分布直方图
        ax2.hist(ke_fluid, bins=40, alpha=0.7, color='orange', edgecolor='black')

        mean_ke = np.mean(ke_fluid)
        median_ke = np.median(ke_fluid)

        ax2.axvline(mean_ke, color='red', linestyle='--', linewidth=2,
                   label=f'Mean: {mean_ke:.8f} J/m³')
        ax2.axvline(median_ke, color='green', linestyle='--', linewidth=2,
                   label=f'Median: {median_ke:.8f} J/m³')

        ax2.set_title('Kinetic Energy Distribution', fontweight='bold')
        ax2.set_xlabel('Kinetic Energy (J/m³)', fontsize=12)
        ax2.set_ylabel('Frequency', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 添加统计信息
        total_ke = np.sum(ke_fluid)
        max_ke = np.max(ke_fluid)
        info_text = f"""动能统计 Kinetic Energy Statistics:
        总动能 Total KE: {total_ke:.6f} J/m³
        最大动能 Max KE: {max_ke:.8f} J/m³
        平均动能 Mean KE: {mean_ke:.8f} J/m³
        动能标准差 KE Std: {np.std(ke_fluid):.8f} J/m³
        """

        fig.text(0.02, 0.02, info_text, fontsize=10, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_performance_radar_plot(self, metrics: Dict[str, Dict[str, float]], save_path: str) -> str:
        """创建性能指标雷达图"""
        fig, ax = plt.subplots(figsize=(10, 10), dpi=500, subplot_kw=dict(projection='polar'))

        if not metrics:
            ax.text(0.5, 0.5, 'No Performance Metrics',
                   ha='center', va='center', transform=ax.transAxes, fontsize=16)
            ax.set_title('Performance Radar Chart', fontsize=16, fontweight='bold')
            fig.savefig(save_path, dpi=500, bbox_inches='tight')
            plt.close()
            return save_path

        # 选择数值型指标
        all_metrics = set()
        for alg_metrics in metrics.values():
            all_metrics.update(alg_metrics.keys())

        numeric_metrics = []
        for metric in ['efficiency', 'avg_speed', 'path_length', 'computation_time']:
            if metric in all_metrics:
                numeric_metrics.append(metric)

        if not numeric_metrics:
            ax.text(0.5, 0.5, 'No Numeric Metrics Available',
                   ha='center', va='center', transform=ax.transAxes, fontsize=16)
            ax.set_title('Performance Radar Chart', fontsize=16, fontweight='bold')
            fig.savefig(save_path, dpi=500, bbox_inches='tight')
            plt.close()
            return save_path

        # 计算角度
        angles = np.linspace(0, 2*np.pi, len(numeric_metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合

        colors = ['red', 'blue', 'green', 'orange', 'purple', 'cyan']

        # 归一化数据
        max_values = {}
        for metric in numeric_metrics:
            values = [alg_metrics.get(metric, 0) for alg_metrics in metrics.values()]
            max_values[metric] = max(values) if max(values) > 0 else 1

        for i, (alg_name, alg_metrics) in enumerate(metrics.items()):
            values = []
            for metric in numeric_metrics:
                value = alg_metrics.get(metric, 0)
                # 归一化到0-1范围
                if metric == 'computation_time' or metric == 'path_length':
                    # 时间和路径长度越小越好
                    normalized = 1 - (value / max_values[metric]) if max_values[metric] > 0 else 0
                else:
                    # 其他指标越大越好
                    normalized = value / max_values[metric] if max_values[metric] > 0 else 0
                values.append(max(0, min(1, normalized)))  # 确保在0-1范围内

            values += values[:1]  # 闭合

            color = colors[i % len(colors)]
            ax.plot(angles, values, 'o-', linewidth=2, label=alg_name, color=color)
            ax.fill(angles, values, alpha=0.25, color=color)

        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(numeric_metrics, fontsize=12)
        ax.set_ylim(0, 1)
        ax.set_title('Performance Radar Chart',
                    fontsize=16, fontweight='bold', pad=30)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_extended_boundary_map_plot(self, map_data: Dict, save_path: str) -> str:
        """创建扩展边界地图展示图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8), dpi=500)

        # 提取地图信息
        original_map = map_data['original_map']
        extended_map = map_data['extended_map']
        extension_size = map_data['extension_size']
        start_point = map_data['start_point']
        end_point = map_data['end_point']

        # 左图：原始地图
        im1 = ax1.imshow(original_map, cmap='RdYlBu_r', origin='upper', alpha=0.9)
        ax1.set_title('Original Sea Ice Map (256×256)',
                     fontsize=16, fontweight='bold', pad=20)
        ax1.set_xlabel('X Position', fontsize=14)
        ax1.set_ylabel('Y Position', fontsize=14)

        # 添加原始地图的统计信息
        total_cells = original_map.size
        obstacle_cells = np.sum(original_map == 1)
        fluid_cells = np.sum(original_map == 0)

        info_text1 = f'Map Size: {original_map.shape[0]}×{original_map.shape[1]}\n'
        info_text1 += f'Obstacles: {obstacle_cells} ({obstacle_cells/total_cells*100:.1f}%)\n'
        info_text1 += f'Fluid Region: {fluid_cells} ({fluid_cells/total_cells*100:.1f}%)'

        ax1.text(0.02, 0.98, info_text1, transform=ax1.transAxes,
                verticalalignment='top', fontsize=12, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))

        # 右图：扩展边界地图
        im2 = ax2.imshow(extended_map, cmap='RdYlBu_r', origin='upper', alpha=0.9)
        ax2.set_title('Extended Boundary Map with Inlet/Outlet',
                     fontsize=16, fontweight='bold', pad=20)
        ax2.set_xlabel('X Position', fontsize=14)
        ax2.set_ylabel('Y Position', fontsize=14)

        # 标记入口和出口
        if start_point and end_point:
            # 入口标记 (绿色圆圈)
            ax2.scatter(start_point[1], start_point[0], color='lime', s=300, marker='o',
                       edgecolors='black', linewidth=4, label='Inlet', zorder=10)
            ax2.text(start_point[1], start_point[0]+8, 'Inlet', ha='center', va='bottom',
                    fontsize=14, fontweight='bold', color='white',
                    bbox=dict(boxstyle='round,pad=0.5', facecolor='green', alpha=0.9))

            # 出口标记 (红色方块)
            ax2.scatter(end_point[1], end_point[0], color='red', s=300, marker='s',
                       edgecolors='black', linewidth=4, label='Outlet', zorder=10)
            ax2.text(end_point[1], end_point[0]+8, 'Outlet', ha='center', va='bottom',
                    fontsize=14, fontweight='bold', color='white',
                    bbox=dict(boxstyle='round,pad=0.5', facecolor='red', alpha=0.9))

        # 标记原始地图区域边界
        if extension_size > 0:
            h, w = original_map.shape
            # 绘制原始地图区域的边界框
            from matplotlib.patches import Rectangle
            rect = Rectangle((extension_size-0.5, extension_size-0.5), w, h,
                           linewidth=3, edgecolor='yellow', facecolor='none',
                           linestyle='--', label='Original Map Region')
            ax2.add_patch(rect)

        # 添加扩展地图的统计信息
        ext_total_cells = extended_map.size
        ext_obstacle_cells = np.sum(extended_map == 1)
        ext_fluid_cells = np.sum(extended_map == 0)

        info_text2 = f'Extended Map Size: {extended_map.shape[0]}×{extended_map.shape[1]}\n'
        info_text2 += f'Boundary Extension: {extension_size} pixels\n'
        info_text2 += f'Obstacles: {ext_obstacle_cells} ({ext_obstacle_cells/ext_total_cells*100:.1f}%)\n'
        info_text2 += f'Fluid Region: {ext_fluid_cells} ({ext_fluid_cells/ext_total_cells*100:.1f}%)\n'
        info_text2 += f'Inlet Position: {start_point}\n'
        info_text2 += f'Outlet Position: {end_point}'

        ax2.text(0.02, 0.98, info_text2, transform=ax2.transAxes,
                verticalalignment='top', fontsize=11, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgreen', alpha=0.8))

        # 添加颜色条
        cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8)
        cbar1.set_label('Map Value (0=Fluid, 1=Obstacle)', fontsize=12, fontweight='bold')

        cbar2 = plt.colorbar(im2, ax=ax2, shrink=0.8)
        cbar2.set_label('Map Value (0=Fluid, 1=Obstacle)', fontsize=12, fontweight='bold')

        # 添加图例
        ax2.legend(bbox_to_anchor=(1.05, 0.5), loc='center left', fontsize=12)

        plt.suptitle('Sea Ice Map Boundary Extension Comparison',
                    fontsize=18, fontweight='bold', y=0.95)

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def visualize_boundary_conditions_and_flow_direction(self, map_data: Dict,
                                                        save_path: Optional[str] = None,
                                                        flow_direction_mode: str = 'perpendicular') -> str:
        """
        可视化边界条件和流动方向
        在运行LBM前显示入口出口位置和预期流动方向

        参数:
        map_data: 地图数据字典，包含边界线信息
        save_path: 保存路径
        flow_direction_mode: 流动方向模式
            - 'perpendicular': 垂直于入口边界线（默认）
            - 'point_to_point': 从起点到终点的方向

        返回:
        保存的文件路径
        """
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 获取地图信息
        extended_map = map_data['extended_map']
        start_point = map_data['start_point']
        end_point = map_data['end_point']
        inlet_boundary_line = map_data.get('inlet_boundary_line')
        outlet_boundary_line = map_data.get('outlet_boundary_line')

        # 显示扩展地图
        im = ax.imshow(extended_map, cmap='RdYlBu_r', origin='upper', alpha=0.8)

        # 绘制入口边界线和流动方向
        if inlet_boundary_line:
            # 绘制入口边界线（绿色粗线）- 使用(x,y)格式
            inlet_x = [inlet_boundary_line[0][0], inlet_boundary_line[1][0]]
            inlet_y = [inlet_boundary_line[0][1], inlet_boundary_line[1][1]]
            ax.plot(inlet_x, inlet_y, color='lime', linewidth=6, label='Inlet Boundary', zorder=8)

            # 标记边界线端点
            ax.scatter(inlet_x, inlet_y, color='lime', s=150, marker='o',
                      edgecolors='black', linewidth=2, zorder=9)

            # 计算边界线中心点
            inlet_center_x = (inlet_x[0] + inlet_x[1]) / 2
            inlet_center_y = (inlet_y[0] + inlet_y[1]) / 2

            # 根据流动方向模式计算流动方向
            if flow_direction_mode == 'perpendicular':
                # 模式1：垂直于入口边界线
                line_dx = inlet_boundary_line[1][0] - inlet_boundary_line[0][0]  # x方向
                line_dy = inlet_boundary_line[1][1] - inlet_boundary_line[0][1]  # y方向

                # 计算法向量（垂直于线段）- 与LBM核心完全一致
                if abs(line_dx) < 1e-6:  # 垂直线段（x坐标相同）
                    normal_x = 1.0 if line_dy > 0 else -1.0  # 向右或向左
                    normal_y = 0.0
                elif abs(line_dy) < 1e-6:  # 水平线段（y坐标相同）
                    normal_x = 0.0
                    normal_y = 1.0 if line_dx > 0 else -1.0  # 向下或向上
                else:  # 斜线段
                    # 使用右手法则
                    normal_x = -line_dy
                    normal_y = line_dx
                    # 归一化
                    length = np.sqrt(normal_x**2 + normal_y**2)
                    if length > 0:
                        normal_x /= length
                        normal_y /= length

                flow_direction = (normal_x, normal_y)
                direction_description = "垂直于边界线"

            elif flow_direction_mode == 'point_to_point':
                # 模式2：从起点到终点的方向
                if start_point and end_point:
                    dx = end_point[0] - start_point[0]  # x方向
                    dy = end_point[1] - start_point[1]  # y方向

                    # 归一化
                    length = np.sqrt(dx**2 + dy**2)
                    if length > 0:
                        normal_x = dx / length
                        normal_y = dy / length
                    else:
                        # 如果起点终点相同，默认向右
                        normal_x, normal_y = 1.0, 0.0

                    flow_direction = (normal_x, normal_y)
                    direction_description = "起点到终点"
                else:
                    # 回退到垂直模式
                    line_dx = inlet_boundary_line[1][0] - inlet_boundary_line[0][0]
                    line_dy = inlet_boundary_line[1][1] - inlet_boundary_line[0][1]

                    if abs(line_dx) < 1e-6:
                        normal_x = 1.0 if line_dy > 0 else -1.0
                        normal_y = 0.0
                    elif abs(line_dy) < 1e-6:
                        normal_x = 0.0
                        normal_y = 1.0 if line_dx > 0 else -1.0
                    else:
                        normal_x = -line_dy
                        normal_y = line_dx
                        length = np.sqrt(normal_x**2 + normal_y**2)
                        if length > 0:
                            normal_x /= length
                            normal_y /= length

                    flow_direction = (normal_x, normal_y)
                    direction_description = "垂直于边界线（回退）"
            else:
                raise ValueError(f"不支持的流动方向模式: {flow_direction_mode}")

            # 绘制流动方向箭头（从边界线指向流体区域）
            arrow_length = 20
            ax.arrow(inlet_center_x, inlet_center_y,
                    flow_direction[0] * arrow_length, flow_direction[1] * arrow_length,
                    head_width=5, head_length=3, fc='darkgreen', ec='darkgreen',
                    linewidth=3, zorder=10, label='Inlet Flow Direction')

            # 添加入口标注
            ax.text(inlet_center_x - 15, inlet_center_y - 15, 'INLET\n(Flow Direction)',
                   ha='center', va='center', fontsize=12, fontweight='bold',
                   color='white', bbox=dict(boxstyle='round,pad=0.5',
                   facecolor='green', alpha=0.9), zorder=11)

        # 绘制出口边界线
        if outlet_boundary_line:
            # 绘制出口边界线（红色粗线）- 使用(x,y)格式
            outlet_x = [outlet_boundary_line[0][0], outlet_boundary_line[1][0]]
            outlet_y = [outlet_boundary_line[0][1], outlet_boundary_line[1][1]]
            ax.plot(outlet_x, outlet_y, color='red', linewidth=6, label='Outlet Boundary', zorder=8)

            # 标记边界线端点
            ax.scatter(outlet_x, outlet_y, color='red', s=150, marker='s',
                      edgecolors='black', linewidth=2, zorder=9)

            # 计算边界线中心点
            outlet_center_x = (outlet_x[0] + outlet_x[1]) / 2
            outlet_center_y = (outlet_y[0] + outlet_y[1]) / 2

            # 添加出口标注
            ax.text(outlet_center_x + 15, outlet_center_y + 15, 'OUTLET',
                   ha='center', va='center', fontsize=12, fontweight='bold',
                   color='white', bbox=dict(boxstyle='round,pad=0.5',
                   facecolor='red', alpha=0.9), zorder=11)

        # 标记入口和出口中心点 - 使用(x,y)格式
        if start_point:
            ax.scatter(start_point[0], start_point[1], color='lime', s=300, marker='o',
                      edgecolors='black', linewidth=4, label='Inlet Center', zorder=10)

        if end_point:
            ax.scatter(end_point[0], end_point[1], color='red', s=300, marker='s',
                      edgecolors='black', linewidth=4, label='Outlet Center', zorder=10)

        # 绘制预期流动路径（从入口到出口的大致方向）
        if start_point and end_point:
            # 绘制虚线表示主要流动方向 - 使用(x,y)格式
            ax.plot([start_point[0], end_point[0]], [start_point[1], end_point[1]],
                   color='yellow', linewidth=3, linestyle='--', alpha=0.8,
                   label='Expected Flow Path', zorder=7)

            # 在路径中点添加流动方向箭头
            mid_x = (start_point[0] + end_point[0]) / 2
            mid_y = (start_point[1] + end_point[1]) / 2
            flow_dx = end_point[0] - start_point[0]
            flow_dy = end_point[1] - start_point[1]
            flow_length = np.sqrt(flow_dx**2 + flow_dy**2)
            if flow_length > 0:
                flow_dx /= flow_length
                flow_dy /= flow_length
                ax.arrow(mid_x - flow_dx*10, mid_y - flow_dy*10,
                        flow_dx*20, flow_dy*20,
                        head_width=8, head_length=5, fc='orange', ec='orange',
                        linewidth=4, zorder=10)

        # 设置标题和标签
        ax.set_title('LBM Boundary Conditions and Flow Direction Setup',
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 添加图例
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

        # 添加边界条件信息文本
        info_text = "Boundary Conditions:\n"
        info_text += f"• Flow Mode: {flow_direction_mode} ({direction_description})\n"
        if inlet_boundary_line:
            info_text += f"• Inlet Line: {inlet_boundary_line[0]} → {inlet_boundary_line[1]}\n"
            info_text += f"• Flow Direction: ({flow_direction[0]:.3f}, {flow_direction[1]:.3f})\n"
        if outlet_boundary_line:
            info_text += f"• Outlet Line: {outlet_boundary_line[0]} → {outlet_boundary_line[1]}\n"
        info_text += f"• Map Size: {extended_map.shape[0]}×{extended_map.shape[1]}\n"
        info_text += f"• Obstacles: {np.sum(extended_map == 1)} cells\n"
        info_text += f"• Fluid Region: {np.sum(extended_map == 0)} cells"

        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=10, fontweight='bold',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.9))

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Map Values (0=Fluid, 1=Obstacle)', fontsize=12, fontweight='bold')

        plt.tight_layout()

        # 保存图片
        if save_path is None:
            save_path = "边界条件和流动方向设置.png"

        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()

        print(f"✅ 边界条件和流动方向图已保存: {save_path}")
        return save_path

    # ==================== 融合对比可视化函数 ====================

    def _create_velocity_field_comparison(self, grid_map: np.ndarray,
                                         traditional_speed: np.ndarray, enhanced_speed: np.ndarray,
                                         start_point: Tuple[int, int], end_point: Tuple[int, int],
                                         save_path: str) -> str:
        """创建速度场对比图"""
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6), dpi=500)

        # 传统LBM速度场
        im1 = ax1.imshow(traditional_speed, cmap='viridis', origin='upper', alpha=0.8)
        ax1.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax1.set_title('Traditional LBM Velocity Field', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X Position', fontsize=12)
        ax1.set_ylabel('Y Position', fontsize=12)

        # 添加起点终点
        if start_point and end_point:
            ax1.scatter(start_point[1], start_point[0], color='lime', s=100, marker='o',
                       edgecolors='black', linewidth=2, zorder=10)
            ax1.scatter(end_point[1], end_point[0], color='red', s=100, marker='s',
                       edgecolors='black', linewidth=2, zorder=10)

        # 增强LBM速度场
        im2 = ax2.imshow(enhanced_speed, cmap='viridis', origin='upper', alpha=0.8)
        ax2.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax2.set_title('Enhanced LBM Velocity Field', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X Position', fontsize=12)
        ax2.set_ylabel('Y Position', fontsize=12)

        # 添加起点终点
        if start_point and end_point:
            ax2.scatter(start_point[1], start_point[0], color='lime', s=100, marker='o',
                       edgecolors='black', linewidth=2, zorder=10)
            ax2.scatter(end_point[1], end_point[0], color='red', s=100, marker='s',
                       edgecolors='black', linewidth=2, zorder=10)

        # 差异图
        speed_diff = enhanced_speed - traditional_speed
        im3 = ax3.imshow(speed_diff, cmap='RdBu_r', origin='upper', alpha=0.8)
        ax3.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax3.set_title('Velocity Difference (Enhanced - Traditional)', fontsize=14, fontweight='bold')
        ax3.set_xlabel('X Position', fontsize=12)
        ax3.set_ylabel('Y Position', fontsize=12)

        # 颜色条
        plt.colorbar(im1, ax=ax1, shrink=0.8, label='Velocity (m/s)')
        plt.colorbar(im2, ax=ax2, shrink=0.8, label='Velocity (m/s)')
        plt.colorbar(im3, ax=ax3, shrink=0.8, label='Velocity Difference (m/s)')

        # 添加统计信息
        trad_max = np.max(traditional_speed)
        enh_max = np.max(enhanced_speed)
        improvement = (enh_max - trad_max) / trad_max * 100 if trad_max > 0 else 0

        fig.suptitle(f'Velocity Field Comparison (Max Speed Improvement: {improvement:.1f}%)',
                    fontsize=16, fontweight='bold')

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_vorticity_comparison(self, grid_map: np.ndarray,
                                   traditional_vorticity: np.ndarray, enhanced_vorticity: np.ndarray,
                                   start_point: Tuple[int, int], end_point: Tuple[int, int],
                                   save_path: str) -> str:
        """创建涡度场对比图"""
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6), dpi=500)

        # 计算涡度范围
        vort_max = max(np.percentile(np.abs(traditional_vorticity), 95),
                      np.percentile(np.abs(enhanced_vorticity), 95))
        vmin, vmax = -vort_max, vort_max

        # 传统LBM涡度场
        im1 = ax1.imshow(traditional_vorticity, cmap='seismic', origin='upper',
                        alpha=0.8, vmin=vmin, vmax=vmax)
        ax1.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax1.set_title('Traditional LBM Vorticity', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X Position', fontsize=12)
        ax1.set_ylabel('Y Position', fontsize=12)

        # 增强LBM涡度场
        im2 = ax2.imshow(enhanced_vorticity, cmap='seismic', origin='upper',
                        alpha=0.8, vmin=vmin, vmax=vmax)
        ax2.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax2.set_title('Enhanced LBM Vorticity', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X Position', fontsize=12)
        ax2.set_ylabel('Y Position', fontsize=12)

        # 差异图
        vort_diff = enhanced_vorticity - traditional_vorticity
        im3 = ax3.imshow(vort_diff, cmap='RdBu_r', origin='upper', alpha=0.8)
        ax3.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax3.set_title('Vorticity Difference (Enhanced - Traditional)', fontsize=14, fontweight='bold')
        ax3.set_xlabel('X Position', fontsize=12)
        ax3.set_ylabel('Y Position', fontsize=12)

        # 颜色条
        plt.colorbar(im1, ax=ax1, shrink=0.8, label='Vorticity (1/s)')
        plt.colorbar(im2, ax=ax2, shrink=0.8, label='Vorticity (1/s)')
        plt.colorbar(im3, ax=ax3, shrink=0.8, label='Vorticity Difference (1/s)')

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_pressure_comparison(self, grid_map: np.ndarray,
                                  traditional_rho: np.ndarray, enhanced_rho: np.ndarray,
                                  start_point: Tuple[int, int], end_point: Tuple[int, int],
                                  save_path: str) -> str:
        """创建压力场对比图"""
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6), dpi=500)

        # 传统LBM压力场
        im1 = ax1.imshow(traditional_rho, cmap='RdYlBu_r', origin='upper', alpha=0.8)
        ax1.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax1.set_title('Traditional LBM Pressure', fontsize=14, fontweight='bold')
        ax1.set_xlabel('X Position', fontsize=12)
        ax1.set_ylabel('Y Position', fontsize=12)

        # 增强LBM压力场
        im2 = ax2.imshow(enhanced_rho, cmap='RdYlBu_r', origin='upper', alpha=0.8)
        ax2.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax2.set_title('Enhanced LBM Pressure', fontsize=14, fontweight='bold')
        ax2.set_xlabel('X Position', fontsize=12)
        ax2.set_ylabel('Y Position', fontsize=12)

        # 差异图
        pressure_diff = enhanced_rho - traditional_rho
        im3 = ax3.imshow(pressure_diff, cmap='RdBu_r', origin='upper', alpha=0.8)
        ax3.contour(grid_map, levels=[0.5], colors='black', linewidths=2)
        ax3.set_title('Pressure Difference (Enhanced - Traditional)', fontsize=14, fontweight='bold')
        ax3.set_xlabel('X Position', fontsize=12)
        ax3.set_ylabel('Y Position', fontsize=12)

        # 颜色条
        plt.colorbar(im1, ax=ax1, shrink=0.8, label='Pressure (Pa)')
        plt.colorbar(im2, ax=ax2, shrink=0.8, label='Pressure (Pa)')
        plt.colorbar(im3, ax=ax3, shrink=0.8, label='Pressure Difference (Pa)')

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_convergence_comparison(self, traditional_history: List[float],
                                     enhanced_history: List[float], save_path: str) -> str:
        """创建收敛性能对比图"""
        fig, ax = plt.subplots(figsize=(12, 8), dpi=500)

        if traditional_history:
            trad_iterations = np.arange(len(traditional_history))
            ax.semilogy(trad_iterations, traditional_history, 'r-', linewidth=3,
                       label='Traditional LBM', alpha=0.8)

        if enhanced_history:
            enh_iterations = np.arange(len(enhanced_history))
            ax.semilogy(enh_iterations, enhanced_history, 'b-', linewidth=3,
                       label='Enhanced LBM', alpha=0.8)

        ax.set_title('Convergence Performance Comparison', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Iteration', fontsize=14)
        ax.set_ylabel('Maximum Velocity (log scale)', fontsize=14)
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)

        # 添加统计信息
        if traditional_history and enhanced_history:
            trad_final = traditional_history[-1]
            enh_final = enhanced_history[-1]
            improvement = (trad_final - enh_final) / trad_final * 100 if trad_final > 0 else 0

            info_text = f'Final Error Reduction: {improvement:.1f}%\n'
            info_text += f'Traditional Final: {trad_final:.2e}\n'
            info_text += f'Enhanced Final: {enh_final:.2e}'

            ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=11,
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def _create_metrics_comparison(self, traditional_metrics: Dict[str, float],
                                 enhanced_metrics: Dict[str, float], save_path: str) -> str:
        """创建性能指标对比图"""
        fig, ax = plt.subplots(figsize=(12, 8), dpi=500)

        # 提取共同的指标
        common_metrics = set(traditional_metrics.keys()) & set(enhanced_metrics.keys())
        if not common_metrics:
            ax.text(0.5, 0.5, 'No Common Metrics Found', ha='center', va='center',
                   transform=ax.transAxes, fontsize=16)
            ax.set_title('Performance Metrics Comparison', fontsize=16, fontweight='bold')
            fig.savefig(save_path, dpi=500, bbox_inches='tight')
            plt.close()
            return save_path

        metrics_list = list(common_metrics)
        trad_values = [traditional_metrics[m] for m in metrics_list]
        enh_values = [enhanced_metrics[m] for m in metrics_list]

        x = np.arange(len(metrics_list))
        width = 0.35

        bars1 = ax.bar(x - width/2, trad_values, width, label='Traditional LBM',
                      color='red', alpha=0.7)
        bars2 = ax.bar(x + width/2, enh_values, width, label='Enhanced LBM',
                      color='blue', alpha=0.7)

        ax.set_title('Performance Metrics Comparison', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Metrics', fontsize=14)
        ax.set_ylabel('Values', fontsize=14)
        ax.set_xticks(x)
        ax.set_xticklabels(metrics_list, rotation=45, ha='right')
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=10)

        for bar in bars2:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path