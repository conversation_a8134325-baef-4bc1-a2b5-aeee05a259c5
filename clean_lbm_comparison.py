"""
清洁的LBM算法对比程序
使用新的可视化系统，专注于单图展示和算法性能对比
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import numpy as np
from utils import DataProcessor, FluidAnalyzer
from fluid_path_planner import FluidSimulator
from clean_visualization import CleanFluidVisualization


class CleanLBMComparison:
    """清洁的LBM算法对比类"""

    def __init__(self):
        """初始化对比器"""
        self.visualizer = CleanFluidVisualization()
        
        # 算法参数
        self.algorithm_params = {
            'traditional_lbm': {
                'tau': 0.8,
                'inlet_velocity': 0.1,
                'max_iterations': 1000,
                'boundary_type': 'zou_he',
                'collision_model': 'bgk',
                'flow_direction_mode': 'perpendicular',
                'convergence_threshold': 1e-6,
            },
            'enhanced_lbm': {
                'tau': 0.6,
                'inlet_velocity': 0.08,
                'max_iterations': 1000,
                'boundary_type': 'zou_he',
                'collision_model': 'bgk',
                'flow_direction_mode': 'point_to_point',
                'convergence_threshold': 1e-7,
            }
        }

    def load_sea_ice_map(self):
        """加载海冰地图数据"""
        try:
            map_data = DataProcessor.load_sea_ice_map("sea_ice_419.xlsx")
            if map_data is not None:
                print("✅ 海冰地图加载成功")
                return map_data
            else:
                print("⚠️ 海冰地图加载失败，使用测试网格")
                return self.create_test_grid()
        except Exception as e:
            print(f"❌ 地图加载错误: {e}")
            return self.create_test_grid()

    def create_test_grid(self):
        """创建测试网格"""
        grid_map = np.zeros((256, 256), dtype=int)
        # 添加一些障碍物
        grid_map[100:150, 120:140] = 1  # 中央障碍物
        grid_map[50:80, 50:80] = 1      # 左上障碍物
        grid_map[180:210, 180:210] = 1  # 右下障碍物
        
        return {
            'original_map': grid_map,
            'extended_map': grid_map,
            'extension_size': 0,
            'start_point': (15, 15),
            'end_point': (240, 240),
            'original_shape': grid_map.shape,
            'inlet_boundary_line': [(10, 10), (10, 20)],
            'outlet_boundary_line': [(245, 245), (245, 255)]
        }

    def run_algorithm(self, algorithm_name: str, grid_map: np.ndarray, map_data: dict):
        """运行单个算法"""
        params = self.algorithm_params[algorithm_name]
        
        print(f"\n🔬 运行{algorithm_name}...")
        print(f"  参数: tau={params['tau']}, velocity={params['inlet_velocity']}")
        print(f"  流动方向模式: {params['flow_direction_mode']}")
        
        # 创建模拟器
        simulator = FluidSimulator(
            grid_map=grid_map,
            start_point=map_data['start_point'],
            end_point=map_data['end_point'],
            map_data=map_data
        )
        
        # 准备网格
        simulator.prepare_grid_with_inlet_outlet()
        
        # 运行模拟
        import time
        start_time = time.time()
        
        simulator.run_fluid_simulation(
            max_iter=params['max_iterations'],
            tau=params['tau'],
            inlet_velocity=params['inlet_velocity'],
            convergence_threshold=params['convergence_threshold'],
            collision_model=params['collision_model'],
            boundary_condition=params['boundary_type'],
            flow_direction_mode=params['flow_direction_mode']
        )
        
        simulation_time = time.time() - start_time
        
        # 提取结果
        results = {
            'simulator': simulator,
            'simulation_time': simulation_time,
            'u': simulator.u,
            'v': simulator.v,
            'rho': simulator.rho,
            'speed': simulator.speed,
            'vorticity': simulator.vorticity,
            'convergence_history': simulator.convergence_history,
            'grid_map': simulator.grid_map,
            'algorithm_name': algorithm_name,
            'flow_direction_mode': params['flow_direction_mode'],
            'metrics': {
                'simulation_time': simulation_time,
                'final_max_velocity': np.max(simulator.speed) if simulator.speed is not None else 0,
                'velocity_smoothness': 1 / (1 + np.std(simulator.speed)) if simulator.speed is not None else 0,
                'avg_oscillation': 0.0  # 简化版本
            }
        }
        
        print(f"  ✅ {algorithm_name}完成，耗时: {simulation_time:.3f}秒")
        print(f"  最大速度: {results['metrics']['final_max_velocity']:.6f}")
        
        return results

    def extract_original_region(self, data: np.ndarray, map_data: dict) -> np.ndarray:
        """提取原始256x256区域的数据"""
        extension_size = map_data.get('extension_size', 0)
        if extension_size > 0:
            # 从扩展地图中提取中央的原始区域
            start_idx = extension_size
            end_idx = -extension_size
            return data[start_idx:end_idx, start_idx:end_idx]
        else:
            return data

    def run_comparison(self):
        """运行完整的算法对比"""
        print("🚀 开始清洁LBM算法对比")
        print("=" * 80)
        
        # 1. 加载地图数据
        print("1️⃣ 加载地图数据...")
        map_data = self.load_sea_ice_map()
        
        # 2. 运行传统LBM
        traditional_results = self.run_algorithm(
            'traditional_lbm', 
            map_data['original_map'], 
            map_data
        )
        
        # 3. 运行增强LBM
        enhanced_results = self.run_algorithm(
            'enhanced_lbm', 
            map_data['original_map'], 
            map_data
        )
        
        # 4. 提取原始区域数据
        print("\n4️⃣ 提取原始区域数据...")
        
        # 传统LBM数据
        trad_u_orig = self.extract_original_region(traditional_results['u'], map_data)
        trad_v_orig = self.extract_original_region(traditional_results['v'], map_data)
        trad_rho_orig = self.extract_original_region(traditional_results['rho'], map_data)
        trad_speed_orig = self.extract_original_region(traditional_results['speed'], map_data)
        trad_vorticity_orig = FluidAnalyzer.calculate_vorticity(trad_u_orig, trad_v_orig)
        
        # 增强LBM数据
        enh_u_orig = self.extract_original_region(enhanced_results['u'], map_data)
        enh_v_orig = self.extract_original_region(enhanced_results['v'], map_data)
        enh_rho_orig = self.extract_original_region(enhanced_results['rho'], map_data)
        enh_speed_orig = self.extract_original_region(enhanced_results['speed'], map_data)
        enh_vorticity_orig = FluidAnalyzer.calculate_vorticity(enh_u_orig, enh_v_orig)
        
        print(f"  原始区域尺寸: {trad_speed_orig.shape}")
        
        # 5. 生成单图可视化
        print("\n5️⃣ 生成单图可视化...")
        
        output_dir = "清洁可视化结果"
        os.makedirs(output_dir, exist_ok=True)
        
        # 传统LBM单图
        print("  生成传统LBM单图...")
        trad_dir = os.path.join(output_dir, "传统LBM单图")
        trad_files = self.visualizer.create_single_field_visualizations(
            map_data=map_data,
            u=trad_u_orig,
            v=trad_v_orig,
            rho=trad_rho_orig,
            speed=trad_speed_orig,
            vorticity=trad_vorticity_orig,
            output_dir=trad_dir
        )
        
        # 增强LBM单图
        print("  生成增强LBM单图...")
        enh_dir = os.path.join(output_dir, "增强LBM单图")
        enh_files = self.visualizer.create_single_field_visualizations(
            map_data=map_data,
            u=enh_u_orig,
            v=enh_v_orig,
            rho=enh_rho_orig,
            speed=enh_speed_orig,
            vorticity=enh_vorticity_orig,
            output_dir=enh_dir
        )
        
        # 6. 生成算法对比图
        print("\n6️⃣ 生成算法对比图...")
        
        comparison_dir = os.path.join(output_dir, "算法对比图")
        
        traditional_data = {
            'speed': trad_speed_orig,
            'vorticity': trad_vorticity_orig,
            'rho': trad_rho_orig,
            'grid_map': map_data['original_map'],
            'convergence_history': traditional_results['convergence_history'],
            'metrics': traditional_results['metrics']
        }
        
        enhanced_data = {
            'speed': enh_speed_orig,
            'vorticity': enh_vorticity_orig,
            'rho': enh_rho_orig,
            'grid_map': map_data['original_map'],
            'convergence_history': enhanced_results['convergence_history'],
            'metrics': enhanced_results['metrics']
        }
        
        comparison_files = self.visualizer.create_algorithm_comparison_plots(
            traditional_data=traditional_data,
            enhanced_data=enhanced_data,
            output_dir=comparison_dir
        )
        
        # 7. 性能总结
        print("\n7️⃣ 性能对比总结...")
        print("=" * 60)
        
        trad_metrics = traditional_results['metrics']
        enh_metrics = enhanced_results['metrics']
        
        print(f"📊 算法性能对比:")
        print(f"  传统LBM模拟时间: {trad_metrics['simulation_time']:.3f}秒")
        print(f"  增强LBM模拟时间: {enh_metrics['simulation_time']:.3f}秒")
        
        if trad_metrics['simulation_time'] > 0:
            time_improvement = (trad_metrics['simulation_time'] - enh_metrics['simulation_time']) / trad_metrics['simulation_time'] * 100
            print(f"  时间改进: {time_improvement:.1f}%")
        
        print(f"  传统LBM最大速度: {trad_metrics['final_max_velocity']:.6f}")
        print(f"  增强LBM最大速度: {enh_metrics['final_max_velocity']:.6f}")
        
        if trad_metrics['final_max_velocity'] > 0:
            speed_improvement = (enh_metrics['final_max_velocity'] - trad_metrics['final_max_velocity']) / trad_metrics['final_max_velocity'] * 100
            print(f"  速度改进: {speed_improvement:.1f}%")
        
        print(f"\n🧭 流动方向模式对比:")
        print(f"  传统LBM: {traditional_results['flow_direction_mode']}")
        print(f"  增强LBM: {enhanced_results['flow_direction_mode']}")
        
        print(f"\n📁 生成的文件:")
        print(f"  传统LBM单图: {len(trad_files)} 个文件")
        print(f"  增强LBM单图: {len(enh_files)} 个文件")
        print(f"  算法对比图: {len(comparison_files)} 个文件")
        print(f"  总输出目录: {output_dir}")
        
        print("\n🎉 清洁LBM算法对比完成！")
        
        return {
            'traditional_files': trad_files,
            'enhanced_files': enh_files,
            'comparison_files': comparison_files,
            'output_dir': output_dir
        }


if __name__ == "__main__":
    # 运行清洁的LBM算法对比
    comparison = CleanLBMComparison()
    results = comparison.run_comparison()
    
    print(f"\n🎯 所有可视化文件已生成到: {results['output_dir']}")
    print("包含以下内容:")
    print("  • 传统LBM单图: 速度场、速度X/Y分量、涡量场、压力场、流线场、三维图")
    print("  • 增强LBM单图: 速度场、速度X/Y分量、涡量场、压力场、流线场、三维图")
    print("  • 算法对比图: 收敛曲线对比、速度场对比、涡量场对比、压力场对比、性能指标对比")
